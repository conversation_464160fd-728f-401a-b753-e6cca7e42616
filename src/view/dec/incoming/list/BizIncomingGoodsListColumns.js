import {baseColumns} from "@/view/common/baseColumns";

import {h, reactive, ref} from "vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()





export function getColumns() {

  const commColumns = reactive([
    'id',
    'businessType',
    'dataState',
    'versionNo',
    'tradeCode',
    'sysOrgCode',
    'parentId',
    'createBy',
    'createTime',
    'updateBy',
    'updateTime',
    'insertUserName',
    'updateUserName',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'goodsName',
    'productModel',
    'quantity',
    'unit',
    'unitPrice',
    'amount',
    'deliveryDate',
    'totalUsd',
    'remarks',
    'headId',
    'inQuantity',
    'inUnit',
    'curr',
    'invoiceNo'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    // {
    //   width: 150,
    //   title: '操作',
    //   dataIndex: 'operation',
    //   key: 'operation',
    //   align: 'center',
    //   fixed: 'left',
    // },
    // {
    //   title: '主键id',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'id',
    //   key: 'id',
    // },
    // {
    //   title: '业务类型',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'businessType',
    //   key: 'businessType',
    // },
    // {
    //   title: '数据状态',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'dataState',
    //   key: 'dataState',
    // },
    // {
    //   title: '版本号',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'versionNo',
    //   key: 'versionNo',
    // },
    // {
    //   title: '企业10位编码',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'tradeCode',
    //   key: 'tradeCode',
    // },
    // {
    //   title: '组织机构代码',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'sysOrgCode',
    //   key: 'sysOrgCode',
    // },
    // {
    //   title: '父级id',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'parentId',
    //   key: 'parentId',
    // },
    // {
    //   title: '创建人',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'createBy',
    //   key: 'createBy',
    // },
    // {
    //   title: '创建时间',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'createTime',
    //   key: 'createTime',
    // },
    // {
    //   title: '更新人',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'updateBy',
    //   key: 'updateBy',
    // },
    // {
    //   title: '更新时间',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'updateTime',
    //   key: 'updateTime',
    // },
    // {
    //   title: '插入用户名',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'insertUserName',
    //   key: 'insertUserName',
    // },
    // {
    //   title: '更新用户名',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'updateUserName',
    //   key: 'updateUserName',
    // },
    // {
    //   title: '扩展字段1',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend1',
    //   key: 'extend1',
    // },
    // {
    //   title: '扩展字段2',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend2',
    //   key: 'extend2',
    // },
    // {
    //   title: '扩展字段3',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend3',
    //   key: 'extend3',
    // },
    // {
    //   title: '扩展字段4',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend4',
    //   key: 'extend4',
    // },
    // {
    //   title: '扩展字段5',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend5',
    //   key: 'extend5',
    // },
    // {
    //   title: '扩展字段6',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend6',
    //   key: 'extend6',
    // },
    // {
    //   title: '扩展字段7',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend7',
    //   key: 'extend7',
    // },
    // {
    //   title: '扩展字段8',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend8',
    //   key: 'extend8',
    // },
    // {
    //   title: '扩展字段9',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend9',
    //   key: 'extend9',
    // },
    // {
    //   title: '扩展字段10',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend10',
    //   key: 'extend10',
    // },
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'goodsName',
      key: 'goodsName',
    },
    {
      title: '规格',
      width: 200,
      align: 'center',
      dataIndex: 'productModel',
      key: 'productModel',
    },
    {
      title: '进口数量',
      width: 200,
      align: 'center',
      dataIndex: 'inQuantity',
      key: 'inQuantity',
    },
    {
      title: '进口单位',
      width: 200,
      align: 'center',
      dataIndex: 'inUnit',
      key: 'inUnit',
    },
    {
      title: '数量',
      width: 200,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
    },

    {
      title: '单价',
      width: 200,
      align: 'center',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
    },
    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
    },
    {
      title: '进口发票号',
      width: 200,
      align: 'center',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo'
    }
    // {
    //   title: '交货日期',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'deliveryDate',
    //   key: 'deliveryDate',
    // },
    // {
    //   title: '总价折美元',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'totalUsd',
    //   key: 'totalUsd',
    // },
    // {
    //   title: '备注',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'remarks',
    //   key: 'remarks',
    // },
    // {
    //   title: '表头head_id',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'headId',
    //   key: 'headId',
    // },
    //
    // {
    //   title: '币种',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'curr',
    //   key: 'curr',
    // },



  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
