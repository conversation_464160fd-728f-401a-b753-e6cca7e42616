<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:bi:add']">
          <a-button size="small"  >
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            维护进口发票号
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:bi:delete']">
          <a-button  size="small">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            发票汇总显示
          </a-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-table-item remove-table-border-add-bg"
          size="small"
          :height="430"
          :scroll="{ y:'100%', x: 400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import { onMounted, reactive, ref} from "vue";
import {getColumns} from "@/view/dec/incoming/list/BizIncomingGoodsListColumns"
const { totalColumns } = getColumns()
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";


/* 定义接收数据  */
const props = defineProps({
  /* 表头headId */
  headId: {
    type: String,
    default: () => ''
  },
  /* 是否能编辑 */
  isEdit: {
    type: String,
    default: () => '0'
  },
  /* 是否查看模式 */
  showDisable:{
    type: Boolean,
    default: () => false
  }
});


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  operationEdit,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  ajaxUrl,
  handlerRefresh

} = useCommon()



defineOptions({
  name: 'BizIncomingGoodsList',
});




/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});



/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};



/* 获取列表 */
// 方法定义
const getList = async () => {
  tableLoading.value = true
  try {
    const res =  await window.majesty.httpUtil.postAction(`${ycCsApi.bizInComingList.list}?page=${page.current}&limit=${page.pageSize}`,
      { headId: props.headId }
    );
    dataSourceList.value = res.data
    page.total = res.total
  }catch(err) {
    console.log(err)
    message.error(err.message)
  }finally {
    tableLoading.value = false
  }

}


const onPageChange = async (pageNumber, pageSize) =>{
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  await getList()
}


onMounted(fn => {



  getList()


})









</script>

<style lang="less" scoped>


</style>
