<template>
  <a-form layout="inline" label-align="right" :label-col="{ style: { width: '100px' } }" :model="searchParam"
          class="cs-form grid-container">




    <!--  进货单状态  -->
    <a-form-item name="dataState" label="进货单状态" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.dataState" id="salesDataStatus">
        <a-select-option v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!--  进货单号  -->
    <a-form-item name="purchaseNo" label="进货单号" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.purchaseNo"/>
    </a-form-item>


    <!--  合同号  -->
    <a-form-item name="contractNo" label="合同号" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.contractNo"/>
    </a-form-item>


    <!--  供应商 汇总供应商列表 -->
    <a-form-item name="supplier" label="供应商" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.supplier"/>
    </a-form-item>

    <!--  客户 汇总客户列表  -->
    <a-form-item name="customer" label="客户" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.customer"/>
    </a-form-item>


    <!--  创建时间   -->
    <a-form-item name="createTime" label="制单日期" class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeForm"
              id="createTimeForm"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createTimeTo"
              id="createTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>





  </a-form>
</template>

<script setup>

  import {inject, onMounted, reactive} from 'vue'
  import {productClassify} from "@/view/common/constant";
  import CsSelect from "@/components/select/CsSelect.vue";

  defineOptions({
    name: 'BizIncomingGoodsHeadSearch'
  })


  const searchParam = reactive({
    id: '',
    businessType: '',
    dataState: '',
    versionNo: '',
    tradeCode: '',
    sysOrgCode: '',
    parentId: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    insertUserName: '',
    updateUserName: '',
    extend1: '',
    extend2: '',
    extend3: '',
    extend4: '',
    extend5: '',
    extend6: '',
    extend7: '',
    extend8: '',
    extend9: '',
    extend10: '',
    contractNo: '',
    purchaseNo: '',
    customer: '',
    supplier: '',
    invoiceNo: '',
    portOfDeparture: '',
    destination: '',
    paymentMethod: '',
    priceTerm: '',
    priceTermPort: '',
    vesselVoyage: '',
    sailingDate: '',
    expectedArrivalDate: '',
    salesDate: '',
    contractAmount: '',
    insuranceRate: '',
    insuranceMarkup: '',
    documentCreator: '',
    documentDate: '',
    documentStatus: '',
    confirmTime: '',
    approvalStatus: '',
    dateOfContract: ''
  })


  /* 定义重置方法(注意前后顺序) */
  const resetSearch = () => {
    Object.keys(searchParam).forEach(key => {
      searchParam[key] = '';
    });
  }

  defineExpose({searchParam, resetSearch});

  onMounted(() => {

  });
</script>

<style lang='less' scoped>

</style>
