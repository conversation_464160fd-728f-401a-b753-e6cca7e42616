<template>
  <section>
    <a-card size="small" title="证件信息" class="cs-card-form">

      <div v-if="formLoading" class="cs-form" style="height:38vh;display: flex;align-items: center;justify-content: center;">
        <a-spin tip="数据加载中..."/>
      </div>
      <div class="cs-form" v-else>

        <a-form  ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
<!--                        &lt;!&ndash; 主键ID &ndash;&gt;-->
<!--            <a-form-item name="id"   :label="'主键ID'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.id" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 业务类型 &ndash;&gt;-->
<!--            <a-form-item name="businessType"   :label="'业务类型'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.businessType" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 数据状态 &ndash;&gt;-->
<!--            <a-form-item name="dataState"   :label="'数据状态'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.dataState" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 版本号 &ndash;&gt;-->
<!--            <a-form-item name="versionNo"   :label="'版本号'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.versionNo" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 企业10位编码 &ndash;&gt;-->
<!--            <a-form-item name="tradeCode"   :label="'企业10位编码'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.tradeCode" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 组织机构代码 &ndash;&gt;-->
<!--            <a-form-item name="sysOrgCode"   :label="'组织机构代码'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.sysOrgCode" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 父级ID &ndash;&gt;-->
<!--            <a-form-item name="parentId"   :label="'父级ID'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.parentId" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 创建人 &ndash;&gt;-->
<!--            <a-form-item name="createBy"   :label="'创建人'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.createBy" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 创建时间 &ndash;&gt;-->
<!--            <a-form-item name="createTime"   :label="'创建时间'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.createTime" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 更新人 &ndash;&gt;-->
<!--            <a-form-item name="updateBy"   :label="'更新人'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.updateBy" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 更新时间 &ndash;&gt;-->
<!--            <a-form-item name="updateTime"   :label="'更新时间'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.updateTime" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 插入用户名 &ndash;&gt;-->
<!--            <a-form-item name="insertUserName"   :label="'插入用户名'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.insertUserName" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 更新用户名 &ndash;&gt;-->
<!--            <a-form-item name="updateUserName"   :label="'更新用户名'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.updateUserName" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段1 &ndash;&gt;-->
<!--            <a-form-item name="extend1"   :label="'扩展字段1'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend1" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段2 &ndash;&gt;-->
<!--            <a-form-item name="extend2"   :label="'扩展字段2'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend2" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段3 &ndash;&gt;-->
<!--            <a-form-item name="extend3"   :label="'扩展字段3'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend3" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段4 &ndash;&gt;-->
<!--            <a-form-item name="extend4"   :label="'扩展字段4'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend4" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段5 &ndash;&gt;-->
<!--            <a-form-item name="extend5"   :label="'扩展字段5'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend5" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段6 &ndash;&gt;-->
<!--            <a-form-item name="extend6"   :label="'扩展字段6'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend6" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段7 &ndash;&gt;-->
<!--            <a-form-item name="extend7"   :label="'扩展字段7'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend7" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段8 &ndash;&gt;-->
<!--            <a-form-item name="extend8"   :label="'扩展字段8'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend8" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段9 &ndash;&gt;-->
<!--            <a-form-item name="extend9"   :label="'扩展字段9'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend9" />-->
<!--            </a-form-item>-->
<!--            &lt;!&ndash; 扩展字段10 &ndash;&gt;-->
<!--            <a-form-item name="extend10"   :label="'扩展字段10'" class="grid-item"  :colon="false">-->
<!--                <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend10" />-->
<!--            </a-form-item>-->
          <!-- 许可证号 -->
          <a-form-item name="licenseNumber"   :label="'许可证号'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.licenseNumber" />
          </a-form-item>
          <!-- 许可证申请日期 -->
          <a-form-item name="licenseApplicationDate"   :label="'许可证申请日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.licenseApplicationDate"
              id="permitApplyDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <!-- 许可证有效日期 -->
          <a-form-item name="licenseValidityDate"   :label="'许可证有效日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.licenseValidityDate"
              id="permitApplyDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <!-- 准运证编号 -->
          <a-form-item name="permitNumber"   :label="'准运证编号'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.permitNumber" />
          </a-form-item>
          <!-- 准运证申办日期 -->
          <a-form-item name="applicationDateForTransportationPermit"   :label="'准运证申办日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.applicationDateForTransportationPermit"
              id="permitApplyDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>

          <!-- 备注 -->
          <a-form-item name="note"   :label="'备注'" class="grid-item merge-3"  :colon="false">
            <a-textarea :disabled="showDisable"  size="small" v-model:value="formData.note"  :autosize="{ minRows: 3, maxRows: 6 }" />
          </a-form-item>






          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right" :loading="buttonLoadingMap.save"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="handlerOnBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
  import {editStatus} from '@/view/common/constant'
  import {message} from "ant-design-vue";
  import {onMounted, reactive, ref} from "vue";
  import {usePCode} from "@/view/common/usePCode";
  import ycCsApi from "@/api/ycCsApi";
  import {useButtonLoading} from "@/view/utils/useBtnLoading";
  const { getPCode } = usePCode()
  const { setLoading,buttonLoadingMap } = useButtonLoading()

  const props = defineProps({
    /* 表头HeadId*/
    headId: {
      type: String,
      default: () => ''
    },
    /* 编辑配置 */
    editConfig: {
      type: Object,
      default: () => ({
        editStatus: editStatus.SHOW,
        editData: {}
      })
    },
    /* 是否能编辑 */
    isEdit: {
      type: String,
      default: () => '0'
    },
    /* 是否查看 */
    operationStatus:{
      type: Boolean,
      default: () => false
    }
  });


  const handlerOnBack = (val) => {
    emit('onEditBack', val);
  };

  // 是否禁用
  const showDisable = ref(false)

  // 表单数据
  const formData = reactive({
    // 主键ID，系统自动生成
    id:'',
    // 业务类型
    businessType:'',
    // 数据状态
    dataState:'',
    // 版本号
    versionNo:'',
    // 交易代码
    tradeCode:'',
    // 系统机构代码
    sysOrgCode:'',
    // 父级ID
    parentId:props.headId,
    // 创建人
    createBy:'',
    // 创建时间
    createTime:'',
    // 更新人
    updateBy:'',
    // 更新时间
    updateTime:'',
    // 插入用户名
    insertUserName:'',
    // 更新用户名
    updateUserName:'',
    // 扩展字段1
    extend1:'',
    // 扩展字段2
    extend2:'',
    // 扩展字段3
    extend3:'',
    // 扩展字段4
    extend4:'',
    // 扩展字段5
    extend5:'',
    // 扩展字段6
    extend6:'',
    // 扩展字段7
    extend7:'',
    // 扩展字段8
    extend8:'',
    // 扩展字段9
    extend9:'',
    // 扩展字段10
    extend10:'',
    // 许可证号
    licenseNumber:'',
    // 许可证申请日期
    licenseApplicationDate:'',
    // 许可证有效日期
    licenseValidityDate:'',
    // 准运证编号
    permitNumber:'',
    // 准运证申办日期
    applicationDateForTransportationPermit:'',
    // 表头head_id
    headId:props.headId,
    // 备注
    note:''
  })
  // 校验规则
  const rules = {
    id:[
      {max: 40, message: '主键ID长度不能超过 40位字节', trigger: 'blur'}
    ],
    businessType:[
      {max: 60, message: '业务类型长度不能超过 60位字节', trigger: 'blur'}
    ],
    dataState:[
      {max: 10, message: '数据状态长度不能超过 10位字节', trigger: 'blur'}
    ],
    versionNo:[
      {max: 10, message: '版本号长度不能超过 10位字节', trigger: 'blur'}
    ],
    tradeCode:[
      {max: 10, message: '企业10位编码长度不能超过 10位字节', trigger: 'blur'}
    ],
    sysOrgCode:[
      {max: 10, message: '组织机构代码长度不能超过 10位字节', trigger: 'blur'}
    ],
    parentId:[
      {max: 40, message: '父级ID长度不能超过 40位字节', trigger: 'blur'}
    ],
    createBy:[
      {max: 50, message: '创建人长度不能超过 50位字节', trigger: 'blur'}
    ],
    createTime:[
    ],
    updateBy:[
      {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
    ],
    updateTime:[
    ],
    insertUserName:[
      {max: 50, message: '插入用户名长度不能超过 50位字节', trigger: 'blur'}
    ],
    updateUserName:[
      {max: 50, message: '更新用户名长度不能超过 50位字节', trigger: 'blur'}
    ],
    extend1:[
      {max: 200, message: '扩展字段1长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend2:[
      {max: 200, message: '扩展字段2长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend3:[
      {max: 200, message: '扩展字段3长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend4:[
      {max: 200, message: '扩展字段4长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend5:[
      {max: 200, message: '扩展字段5长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend6:[
      {max: 200, message: '扩展字段6长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend7:[
      {max: 200, message: '扩展字段7长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend8:[
      {max: 200, message: '扩展字段8长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend9:[
      {max: 200, message: '扩展字段9长度不能超过 200位字节', trigger: 'blur'}
    ],
    extend10:[
      {max: 200, message: '扩展字段10长度不能超过 200位字节', trigger: 'blur'}
    ],
    licenseNumber: [
      {max: 60, message: '许可证号长度不能超过 60位字节', trigger: 'blur'}
    ],
    licenseApplicationDate: [],
    licenseValidityDate: [],
    permitNumber: [
      {max: 200, message: '准运证编号长度不能超过 200位字节', trigger: 'blur'}
    ],
    applicationDateForTransportationPermit: [],
    headId: [
      {max: 40, message: '表头head_id长度不能超过 40位字节', trigger: 'blur'}
    ],
    note: [
      {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
    ]
  }

  const pCode = ref('')

  // 定义子组件事件
  const emit = defineEmits(['onEditBack']);







  // vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
  const formRef = ref(null);

  // 保存
  const handlerSave = async () => {
    try {
      await formRef.value.validate();
      setLoading("save", true);
      const res = await window.majesty.httpUtil.postAction(
        ycCsApi.bizIncomingGoodsDocument.insertOrUpdate,
        formData
      );
      if (res.code === 200) {
        message.success("保存成功！");
      } else {
        message.error(res.message)
        throw new Error(res.message);
      }
    } catch (error) {
      console.error("保存失败:", error);
      // message.error(error.message || "保存失败，请稍后再试");

    } finally {
      setLoading("save", false);
    }
  };


  const formLoading = ref(false)

  const getDocument = async () => {
    try {
      console.log('props.headId', props.headId)
      formLoading.value = true // 开始加载状态

      const url = `${ycCsApi.bizIncomingGoodsDocument.getDocumentByHeadId}/${props.headId}`
      const res = await window.majesty.httpUtil.getAction(url) // 使用 await 等待结果

      if (res.code === 200) {
        Object.assign(formData, res.data) // 成功时赋值
      } else {
        message.error(res.message) // 业务错误提示
      }
    } catch (error) {
      message.error('请求失败，请稍后再试') // 网络错误或异常处理
      console.error('请求异常:', error)
    } finally {
      formLoading.value = false // 结束加载状态
    }
  }





  // 初始化操作
  onMounted(() => {
    getPCode().then(res=>{
      console.log('res',res)
      pCode.value = res;
    })


    // 获取证件信息数据
    getDocument()


    // 初始化数据(初始化数据)
    if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
      showDisable.value = false
    }
    if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
      showDisable.value = true
    }
    formData.parentId=props.headId
    formData.headId=props.headId


    /* 根据headId获取证件信息数据  */



  });


</script>

<style lang="less" scoped>


</style>



