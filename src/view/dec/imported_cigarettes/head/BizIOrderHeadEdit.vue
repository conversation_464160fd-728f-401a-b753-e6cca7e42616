<template>
  <section>

    <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
            :model="formData" >
      <a-card size="small" title="订单信息表头" class="cs-card-form">
        <div class="cs-form grid-container">
          <!-- 业务类型 -->
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 订单数据状态 -->
<!--          <a-form-item name="orderDataStatus" :label="'订单数据状态'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.orderDataStatus"/>
          </a-form-item>-->
          <!-- 合同号 -->
<!--          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">-->
<!--            <a-input :disabled="true" size="small" v-model:value="formData.contractNo"/>-->
<!--          </a-form-item>-->
          <!-- 订单号 -->
          <a-form-item name="orderNo" :label="'订单号'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.dataStatus  !== '0'" size="small" v-model:value="formData.orderNo"/>
          </a-form-item>
          <!-- 客户 -->
          <a-form-item name="partyA" :label="'客户'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.dataStatus  !== '0'" size="small" v-model:value="formData.partyA"/>
          </a-form-item>
          <!-- 供应商 -->
          <a-form-item name="partyB" :label="'供应商'" class="grid-item" :colon="false">
            <!-- <a-input :disabled="showDisable || formData.dataStatus  !== '0'" size="small" v-model:value="formData.partyB"/> -->
            <!-- <a-input :disabled="true" size="small" v-model:value="formData.partyB"/> -->
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.partyB" id="partyB">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 交货日期 -->
          <a-form-item name="deliveryDate" :label="'交货日期'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.dataStatus  !== '0'" size="small" v-model:value="formData.deliveryDate"/>
          </a-form-item>
          <!-- 签订日期 -->
          <a-form-item name="dateOfSigning"   :label="'签订日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable || formData.dataStatus  !== '0'"
              v-model:value="formData.dateOfSigning"
              id="dateOfSigning"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <!-- 付款方式 -->
          <a-form-item name="paymentMethod" :label="'付款方式'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.dataStatus  !== '0'" size="small" v-model:value="formData.paymentMethod"/>
          </a-form-item>
          <!-- 计划编号 -->
          <a-form-item name="planNo"   :label="'计划编号'" class="grid-item"  :colon="false">
            <a-input :disabled="true"  size="small" v-model:value="formData.planNo" />
          </a-form-item>
          <!-- 备注 -->
          <a-form-item name="note"   :label="'备注'" class="grid-item merge-3"  :colon="false">
            <a-textarea :disabled="showDisable || formData.dataStatus  !== '0'" size="small" v-model:value="formData.note" :autoSize="{ minRows: 3, maxRows: 4 }"></a-textarea>
          </a-form-item>
          <!-- 制单人 -->
          <a-form-item  name="insertUserName"  :label="'制单人'" class="grid-item"  :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.insertUserName"/>
          </a-form-item>
          <!-- 订单制单时间 -->
          <a-form-item  name="insertTime"  :label="'订单制单时间'" class="grid-item" :colon="false" >
            <a-date-picker
              :disabled="true"
              v-model:value="formData.insertTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <!-- 订单单据状态 -->
          <a-form-item name="dataStatus"   :label="'订单单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.dataStatus" id="dataStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 订单确认时间 -->
          <a-form-item name="orderConfirmationTime"   :label="'订单确认时间'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.orderConfirmationTime"
              id="orderConfirmationTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>

          <!-- 审批状态 -->
          <a-form-item name="apprStatus"   :label="'审批状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.apprStatus" id="dataStatus">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.orderApprStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 版本号 -->
          <a-form-item name="versionNo"   :label="'版本号'" class="grid-item"  :colon="false">
            <a-input :disabled="true"  size="small" v-model:value="formData.versionNo" />
          </a-form-item>


          <div class="cs-submit-btn merge-3">
            <!-- style="color:#1890ff; -->
<!--            <a-button size="small" type="ghost"   @click="printOrderPdf" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== 'SHOW' ">-->
<!--                  <template #icon>-->
<!--                    <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>-->
<!--                  </template>-->
<!--                  <template #default>-->
<!--                    打印订单(PDF)-->
<!--                  </template>-->
<!--            </a-button>-->
<!--            <a-button size="small" type="ghost"  @click="printOrder" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== 'SHOW' ">-->
<!--              <template #icon>-->
<!--                <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>-->
<!--              </template>-->
<!--              <template #default>-->
<!--                打印订单(EXCEL)-->
<!--              </template>-->
<!--            </a-button>-->

            <a-dropdown>
              <template #overlay>

                <a-menu >
                  <a-menu-item key="1" @click="printOrderPdf">打印PDF</a-menu-item>
                  <a-menu-item key="2" @click="printOrder">打印EXCEL</a-menu-item>
                </a-menu>
              </template>
              <a-button size="small" type="ghost">
                打印订单
                <GlobalIcon class="btn-icon" type="cloud-download"  style="font-size: 14px;"/>
                <DownOutlined />
              </a-button>
            </a-dropdown>

            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right" v-if="!showDisable" :loading="handlerSaveLoading"
                      v-show="props.editConfig.editStatus !== 'SHOW' " :disabled="formData.dataStatus  !== '0'">保存
            </a-button>
            <!--
              去除保存关闭按钮
              <a-button size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right" v-if="!showDisable" :loading="handlerSaveCloseLoading"
                      v-show="props.editConfig.editStatus !== 'SHOW' " :disabled="formData.dataStatus  !== '0'">保存关闭
              </a-button>
            -->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>

            <!-- 确认 -->
            <a-button size="small" type="ghost"   @click="confirmHead" class="cs-margin-right"
                       v-if="!showDisable"
                      v-show="props.editConfig.editStatus !== 'SHOW' " :loading="confirmOrderLoading">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check"  style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>
          </div>

        </div>
      </a-card>
    </a-form>


    <a-card size="small" title="订单信息表体" class="cs-card-form">
      <dec-erp-i-order-list-list  :head-id="props.editConfig.editData.sid" :is-edit="formData.dataStatus" :showDisable="showDisable" ref="orderListRef"/>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {createVNode, onMounted, ref, toRef, watch} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {
  confirmIOrderHead,
  insertBizIOrderHead,
  updateBizErpIOrderHead,
} from "@/api/cs_api_constant";
import DecErpIOrderListList from "@/view/dec/imported_cigarettes/list/BizIOrderListList.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import ycCsApi from "@/api/ycCsApi";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
import useEventBus from "@/view/common/eventBus";

const {emitEvent} = useEventBus()

const { supplierList,getSupplierList} = useOrderColumnsCommon()
getSupplierList()

const {getPCode} = usePCode()


const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  /* 表头传入状态 查看/编辑 */
  operationStatus: {
    type: String,
    default: ''
  }

});

// 订单表体ref
const orderListRef = ref(null)


// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 定义组件名称
defineOptions({
  name: 'BizIOrderHeadEdit'
})


// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = ref({
  // 主建sid
  sid: '',
  // 制单人
  insertUser: '',
  // 订单制单时间
  insertTime: '',
  // 创建人姓名
  insertUserName: '',
  // 更新人
  updateUser: '',
  // 更新时间
  updateTime: '',
  // 更新人姓名
  updateUserName: '',
  // 企业代码
  tradeCode: '',
  // 业务类型
  businessType: '',
  // 订单数据状态
  orderDataStatus: '',
  // 合同号
  contractNo: '',
  // 订单号
  orderNo: '',
  // 客户
  partyA: '中国烟草上海进出口有限责任公司',
  // 供应商
  partyB: '',
  // 交货日期
  deliveryDate: '即发',
  // 付款方式
  paymentMethod: 'T/T',
  // 进货单号
  purchaseOrderNo: '',
  // 进口发票号码
  importInvoiceNo: '',
  // 许可证号
  licenseNo: '',
  // 准运证编号
  transportPermitNo: '',
  // 销售发票号
  salesInvoiceNo: '',
  // 销售合同号
  salesContractNo: '',
  // 进货数据状态
  purchaseDataStatus: '',
  // 销售数据状态
  salesDataStatus: '',
  // 入库回单状态
  inboundReceiptStatus: '',
  // 出库回单状态
  outboundReceiptStatus: '',
  // 签订日期
  dateOfSigning:'',
  // 计划编号
  planNo:'',
  // 订单确认时间
  orderConfirmationTime:'',
  // 审批状态
  apprStatus:'0',
  // 备注
  note:'',
  // 订单单据状态
  dataStatus:'0',
  // 进口合同表头sid
  headId:'',
  // 是否流入下一个节点
  isNext:'',
  // 版本号
  versionNo:'',
  orderListParams:[]
})
// 校验规则
const rules = {
  sid: [
    {max: 50, message: '主建sid长度不能超过 50位字节', trigger: 'blur'}
  ],
  insertUser: [
    {max: 50, message: '制单人长度不能超过 50位字节', trigger: 'blur'}
  ],
  insertTime: [
    {required: true, message: '订单制单时间不能为空', trigger: 'blur'},
    { required: true, message: '订单制单时间不能为空', trigger: 'blur'}
  ],
  insertUserName: [
    {required: true, message: '制单人不能为空', trigger: 'blur'},
    {max: 50, message: '制单人长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateUser: [
    {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateTime: [],
  updateUserName: [
    {max: 50, message: '更新人姓名长度不能超过 50位字节', trigger: 'blur'}
  ],
  tradeCode: [
    {max: 50, message: '企业代码长度不能超过 50位字节', trigger: 'blur'}
  ],
  businessType: [
    {required: true, message: '业务类型不能为空', trigger: 'change'},
    {max: 60, message: '业务类型长度不能超过 60位字节', trigger: 'blur'}
  ],
  orderDataStatus: [
    {max: 10, message: '订单数据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  contractNo: [
    {required: true, message: '合同号不能为空', trigger: 'change'},
    {max: 60, message: '合同号长度不能超过 60位字节', trigger: 'blur'}
  ],
  orderNo: [
    {required: true, message: '订单号不能为空', trigger: 'change'},
    {max: 60, message: '订单号长度不能超过 60位字节', trigger: 'blur'}
  ],
  partyA: [
    {required: true, message: '客户不能为空', trigger: 'change'},
    {max: 200, message: '客户长度不能超过 200位字节', trigger: 'blur'}
  ],
  partyB: [
    {required: true, message: '供应商不能为空', trigger: 'change'},
    {max: 200, message: '供应商长度不能超过 200位字节', trigger: 'blur'}
  ],
  paymentMethod: [
    {required: true, message: '付款方式不能为空', trigger: 'change'},
    {max: 30, message: '付款方式长度不能超过 30位字节', trigger: 'blur'}
  ],
  deliveryDate: [
    { required: true, message: '交货日期不能为空', trigger: 'change' },
    { max:50, message: '交货日期长度不能超过 50位字节', trigger: 'blur'}
  ],
  dateOfSigning:[
    { required: true, message: '签订日期不能为空', trigger: 'change' },
  ],
  planNo:[
    {required: true, message: '计划编号不能为空', trigger: 'change'},
    {max: 60, message: '计划编号长度不能超过 60位字节', trigger: 'blur'}
  ],
  note:[
    {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
  ],

  purchaseOrderNo: [
    {max: 60, message: '进货单号长度不能超过 60位字节', trigger: 'blur'}
  ],
  importInvoiceNo: [
    {max: 60, message: '进口发票号码长度不能超过 60位字节', trigger: 'blur'}
  ],
  licenseNo: [
    {max: 60, message: '许可证号长度不能超过 60位字节', trigger: 'blur'}
  ],
  transportPermitNo: [
    {max: 60, message: '准运证编号长度不能超过 60位字节', trigger: 'blur'}
  ],
  salesInvoiceNo: [
    {max: 60, message: '销售发票号长度不能超过 60位字节', trigger: 'blur'}
  ],
  salesContractNo: [
    {max: 60, message: '销售合同号长度不能超过 60位字节', trigger: 'blur'}
  ],
  purchaseDataStatus: [
    {max: 10, message: '进货数据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  salesDataStatus: [
    {max: 10, message: '销售数据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  inboundReceiptStatus: [
    {max: 10, message: '入库回单状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  outboundReceiptStatus: [
    {max: 10, message: '出库回单状态长度不能超过 10位字节', trigger: 'blur'}
  ],


  orderConfirmationTime:[
  ],
  apprStatus:[
    {max: 10, message: '审批状态长度不能超过 10位字节', trigger: 'blur'}
  ],

}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res => {
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData.value, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData.value, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData.value, props.editConfig.editData);
    showDisable.value = true
  }
});


// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
const handlerSaveCloseLoading = ref(false)
const handlerSaveLoading = ref(false)
// 保存
const handlerSave = () => {

  // console.log('订单表体ref:',orderListRef.value.dataSourceList)
  Object.assign(formData.value, {
    orderListParams:orderListRef.value.dataSourceList
  })
  // console.log('订单表体ref:',formData.value)

  formRef.value
    .validate()
    .then(() => {
      handlerSaveLoading.value = true
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
        insertBizIOrderHead(formData).then((res) => {
          if (res.code === 200) {
            message.success('新增成功!')
            onBack({
              editData: res.data,
              showBody: true,
              editStatus: editStatus.EDIT,
              showBodyReceiptSell:false
            })
            handlerSaveLoading.value = false
          }else {
            message.error(res.message)
            handlerSaveLoading.value = false
          }
        })
      } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
        updateBizErpIOrderHead(formData.value.sid, formData.value).then((res) => {
          if (res.code === 200) {
            message.success('修改成功!')
            // 因为制单人 制单时间 取得是 coalesce(t.update_user_name,t.insert_user_name)
            // 所以更新时 数据返回需要将时间进行额外处理
            Object.assign(formData.value, res.data)
            formData.value.insertTime = res.data.updateTime
            formData.value.insertUserName =  res.data.updateUserName
            // console.log('res',res.data)
            onBack({
              editData: res.data,
              showBody: true,
              editStatus: editStatus.EDIT
            })
            handlerSaveLoading.value = false
            // 刷新订单表体列表
            emitEvent('refreshOrderList')
          }else {
            message.error(res.message)
            handlerSaveLoading.value = false
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};
const handlerSaveClose = () => {
  formRef.value
    .validate()
    .then(() => {
      handlerSaveCloseLoading.value = true
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
        insertBizIOrderHead(formData).then((res) => {
          if (res.code === 200) {
            message.success('新增成功!')
            onBack(true)
            handlerSaveCloseLoading.value = false
          }else {
            message.error(res.message)
            handlerSaveCloseLoading.value = false
          }
        })
      } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
        updateBizErpIOrderHead(formData.value.sid, formData.value).then((res) => {
          if (res.code === 200) {
            message.success('修改成功!')
            formData.value = res.data
            console.log('res',res.data)
            onBack(true)
            handlerSaveCloseLoading.value = false
          }else {
            message.error(res.message)
            handlerSaveCloseLoading.value = false
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
}


/* 打印订单 */
const printOrder = () => {
  const params = {sid:formData.value.sid,sType:'excel'}
  console.log(formData.value.sid,'sid?')
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.bizIOrderHead.print}/${formData.value.sid}/excel`, null,params,'post',null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
  }).finally(() => {
  })
}
const printOrderPdf = () => {
  const params = {sid:formData.value.sid,sType:'pdf'}
  console.log(formData.value.sid,'sid?')
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.bizIOrderHead.print}/${formData.value.sid}/pdf`, null,params,'post',null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
  }).finally(() => {
  })
}



/* 订单表头确认 */
const confirmOrderLoading = ref(false)
const confirmHead = ()=>{
  if (formData.value.dataStatus === '2'){
    message.warning('该数据已作废，不允许进行确认操作！')
    return
  }

  if (formData.value.dataStatus === '1'){
    message.warning('该数据已经确认，无需重复操作！')
    return
  }

  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '是否确认所选项？',
    onOk() {
      confirmOrderLoading.value = true
      confirmIOrderHead(formData.value).then(res => {
        if (res.code === 200) {
          message.success("确认成功！")
          Object.assign(formData.value, res.data)
          onBack({
            editData: res.data,
            showBodyPurchaseHead: true,
            showBody: true,
            editStatus: editStatus.EDIT
          })
        }else {
          message.error(res.message)
        }
      }).finally(() => {
        confirmOrderLoading.value = false
      })
    },
    onCancel() {
      confirmOrderLoading.value = false
    },
  });
}


// watch(props,(newVal)=>{
//   console.log('监控订单表头状态',newVal.operationStatus)
//   if (newVal.operationStatus === editStatus.SHOW){
//     showDisable.value = false
//     formData.value.dataStatus = '1'
//     console.log('showDisable.value',showDisable.value)
//   }
// },{immediate:true,deep:true})


</script>

<style lang="less" scoped>


</style>



