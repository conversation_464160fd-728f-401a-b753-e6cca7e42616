<template>
  <a-modal
    :visible="visible"
    title="选择计划"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="plan-select-modal">
      <div class="search-area">
        <a-form layout="inline">
          <a-form-item label="计划编号">
            <a-input v-model:value="searchParams.planNo" placeholder="请输入计划编号" />
          </a-form-item>
          <a-form-item label="计划年度">
            <a-input v-model:value="searchParams.planYear" placeholder="请输入计划年度" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <div class="table-area">
        <s-table
          ref="tableRef"
          bordered
          :pagination="pagination"
          :loading="loading"
          :data-source="dataSource"
          :columns="columns"
          row-key="sid"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'operation'">
              <a @click="handleSelect(record)">选择</a>
            </template>
          </template>
        </s-table>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";

defineOptions({
  name: 'PlanSelectModal'
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'on-select']);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 当弹窗显示时，加载数据
    fetchPlanList();
  }
});

// 搜索参数
const searchParams = ref({
  planNo: '',
  planYear: ''
});

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条`
});

// 表格列定义
const columns = [
  {
    title: '操作',
    dataIndex: 'operation',
    width: 80,
    align: 'center'
  },
  {
    title: '计划编号',
    dataIndex: 'planNo',
    width: 150,
    align: 'center'
  },
  {
    title: '计划年度',
    dataIndex: 'planYear',
    width: 100,
    align: 'center'
  },
  {
    title: '上下半年',
    dataIndex: 'halfYear',
    width: 100,
    align: 'center'
  },
  {
    title: '计划总金额',
    dataIndex: 'totalAmount',
    width: 150,
    align: 'center'
  },
  {
    title: '计划总数量',
    dataIndex: 'totalQuantity',
    width: 150,
    align: 'center'
  }
];

// 获取计划列表
const fetchPlanList = () => {
  loading.value = true;
  const params = {
    ...searchParams.value,
    page: pagination.value.current,
    limit: pagination.value.pageSize
  };

  window.majesty.httpUtil.postAction(
    `${ycCsApi.auxiliaryMaterials.contract.planList}?page=${params.page}&limit=${params.limit}`,
    params
  ).then(res => {
    if (res.code === 200) {
      dataSource.value = res.data || [];
      pagination.value.total = res.total || 0;
    } else {
      message.error(res.message || '获取计划列表失败');
    }
    loading.value = false;
  }).catch(error => {
    console.error('获取计划列表失败', error);
    loading.value = false;
  });
};

// 处理表格分页、排序、筛选变化
const handleTableChange = (pag) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  fetchPlanList();
};

// 处理搜索
const handleSearch = () => {
  pagination.value.current = 1;
  fetchPlanList();
};

// 处理重置
const handleReset = () => {
  searchParams.value = {
    planNo: '',
    planYear: ''
  };
  pagination.value.current = 1;
  fetchPlanList();
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理选择
const handleSelect = (record) => {
  emit('on-select', record);
  emit('update:visible', false);
};

// 暴露方法
defineExpose({
  fetchPlanList
});
</script>

<style lang="less" scoped>
.plan-select-modal {
  .search-area {
    margin-bottom: 16px;
  }

  .table-area {
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>
