<template>
  <a-modal
    :visible="visible"
    title="选择商品"
    width="1200px"
    :footer="null"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="product-select-modal">
      <div class="search-area">
        <a-form layout="inline">
          <a-form-item label="商品名称">
            <a-input v-model:value="searchParams.productName" placeholder="请输入商品名称" />
          </a-form-item>
          <a-form-item label="产品型号">
            <a-input v-model:value="searchParams.productModel" placeholder="请输入产品型号" />
          </a-form-item>
          <a-form-item label="规格">
            <a-input v-model:value="searchParams.specification" placeholder="请输入规格" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <div class="table-area">
        <s-table
          ref="tableRef"
          bordered
          :pagination="pagination"
          :loading="loading"
          :data-source="dataSource"
          :columns="columns"
          row-key="id"
          :row-selection="rowSelection"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'weight'">
              {{ formatNumber(record.weight) }}
            </template>
          </template>
        </s-table>
      </div>

      <div class="footer-area" style="margin-top: 16px; text-align: right;">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" style="margin-left: 8px" @click="handleConfirm" :disabled="selectedRowKeys.length === 0">
          确定（已选择 {{ selectedRowKeys.length }} 项）
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";

defineOptions({
  name: 'ProductSelectModal'
});

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'on-select']);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 当弹窗显示时，加载数据
    fetchProductList();
  }
});

// 搜索参数
const searchParams = ref({
  productName: '',
  productModel: '',
  specification: ''
});

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条`
});

// 多选功能
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys, rows) => {
    selectedRowKeys.value = keys;
    selectedRows.value = rows;
  },
  getCheckboxProps: (record) => ({
    disabled: false,
    name: record.productName,
  }),
};

// 数字格式化函数
const formatNumber = (num) => {
  if (num === null || num === undefined || num === '') return '';
  const number = parseFloat(num);
  if (isNaN(number)) return num;

  // 如果是整数，显示.0000
  if (Number.isInteger(number)) {
    return number.toFixed(4);
  }
  // 否则保持原有小数位数
  return number.toString();
};

// 表格列定义
const columns = [
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 200,
    align: 'left'
  },
  {
    title: '产品型号',
    dataIndex: 'productModel',
    width: 150,
    align: 'left'
  },
  {
    title: '规格',
    dataIndex: 'specification',
    width: 150,
    align: 'left'
  },
  {
    title: '克重',
    dataIndex: 'weight',
    width: 120,
    align: 'right'
  },
  {
    title: '计量单位',
    dataIndex: 'unit',
    width: 120,
    align: 'center'
  },
  {
    title: '进口计量单位',
    dataIndex: 'importUnit',
    width: 120,
    align: 'center'
  }
];

// 获取商品列表
const fetchProductList = () => {
  loading.value = true;
  const params = {
    ...searchParams.value,
    page: pagination.value.current,
    limit: pagination.value.pageSize
  };

  window.majesty.httpUtil.postAction(
    `${ycCsApi.biClientInfo.list}?page=${params.page}&limit=${params.limit}`,
    params
  ).then(res => {
    if (res.code === 200) {
      dataSource.value = res.data || [];
      pagination.value.total = res.total || 0;
    } else {
      message.error(res.message || '获取商品列表失败');
    }
    loading.value = false;
  }).catch(error => {
    console.error('获取商品列表失败', error);
    loading.value = false;
  });
};

// 处理表格分页、排序、筛选变化
const handleTableChange = (pag) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  fetchProductList();
};

// 处理搜索
const handleSearch = () => {
  pagination.value.current = 1;
  selectedRowKeys.value = [];
  selectedRows.value = [];
  fetchProductList();
};

// 处理重置
const handleReset = () => {
  searchParams.value = {
    productName: '',
    productModel: '',
    specification: ''
  };
  pagination.value.current = 1;
  selectedRowKeys.value = [];
  selectedRows.value = [];
  fetchProductList();
};

// 处理取消
const handleCancel = () => {
  selectedRowKeys.value = [];
  selectedRows.value = [];
  emit('update:visible', false);
};

// 处理确定（多选）
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请选择至少一项商品记录');
    return;
  }
  emit('on-select', selectedRows.value);
  selectedRowKeys.value = [];
  selectedRows.value = [];
  emit('update:visible', false);
};

// 暴露方法
defineExpose({
  fetchProductList
});
</script>

<style lang="less" scoped>
.product-select-modal {
  .search-area {
    margin-bottom: 16px;
  }

  .table-area {
    max-height: 400px;
    overflow-y: auto;
  }

  .footer-area {
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
  }
}
</style>
