<template>
  <div class="contract-detail-table">

    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:add']">
        <a-button size="small" @click="handleAdd" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="plus" style="color:green"/>
          </template>
          {{localeContent('m.common.button.add')}}
        </a-button>
      </div>
      <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:delete']">
        <a-button size="small" :loading="deleteLoading" @click="handlerDelete" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="delete" style="color:red"/>
          </template>
          {{localeContent('m.common.button.delete')}}
        </a-button>
      </div>
    </div>

    <s-table
      ref="tableRef"
      class="cs-action-item-modal-table remove-table-border-add-bg"
      bordered
      :pagination="false"
      :height="500"
      column-drag
      :data-source="tableData"
      :columns="getColumns"
      row-key="id"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'importUnit'">
          {{ formatUnit(record.importUnit) }}
        </template>
        <template v-if="column.dataIndex === 'unit'">
          {{ formatUnit(record.unit) }}
        </template>
        <template v-if="column.dataIndex === 'productCategory'">
          {{ formatGoodsCategory(record.productCategory) }}
        </template>
      </template>

      <template v-if="props.editConfig.editStatus !== editStatus.SHOW" #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer, record }">
        <template v-if="column.dataIndex === 'quantity'">
          <a-input-number
            :ref="editorRef"
            size="small"
            v-model:value="modelValue.value"
            style="width: 100%;height: 24px"
            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            @blur="() => {
              handleContractQuantityChange(record, modelValue.value);
              // save();
              // closeEditor();
            }"
            @keydown.enter="() => {
              handleContractQuantityChange(record, modelValue.value);
              // save();
              // closeEditor();
            }"
            @keydown.esc="closeEditor"
          />
        </template>
        <template v-if="column.dataIndex === 'importQuantity'">
          <a-input-number
            :ref="editorRef"
            size="small"
            v-model:value="modelValue.value"
            style="width: 100%;height: 24px"
            :formatter="value => value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            @blur="() => {
              handleImportQuantityChange(record, modelValue.value);
            }"
            @keydown.enter="() => {
              handleImportQuantityChange(record, modelValue.value);
            }"
            @keydown.esc="closeEditor"
          />
        </template>
        <template v-if="column.dataIndex === 'deliveryDate'">
          <a-date-picker
            :ref="editorRef"
            size="small"
            v-model:value="modelValue.value"
            style="width: 100%"
            valueFormat="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :locale="locale"
            @change="(date) => {
              handleDeliveryDateChange(record, date);
              save();
            }"
            @blur="closeEditor"
            @keydown.esc="closeEditor"
          />
        </template>
      </template>
    </s-table>

    <!-- 商品选择弹窗 -->
    <product-select-modal
      v-model:visible="productModalVisible"
      @on-select="handleProductSelect"
    />

    <!-- 分页 -->
    <div class=cs-pagination>
      <div class="cs-margin-right cs-list-total-data ">
        总进口数量：{{formatNumber(totalData.importQuantity)}} ，总数量：{{formatNumber(totalData.quantity)}} ，总金额：{{formatNumber(totalData.amount)}}，总值折美元：{{formatNumber(totalData.usdTotal)}}
      </div>
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, watch, onMounted, createVNode, computed, h, nextTick} from 'vue';
import {message, Modal} from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {deleteContractList, getContractTotal, updateContractList, addContractList} from "@/api/auxiliaryMaterials/forContract/contractApi";
import { usePCode } from "@/view/common/usePCode";
import {localeContent} from "@/view/utils/commonUtil";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {useCommon} from '@/view/common/useCommon'
import {editStatus} from "@/view/common/constant";
import { useMerchant } from "@/view/common/useMerchant"
import {useColumnsRender} from "@/view/common/useColumnsRender";
import ProductSelectModal from '../components/ProductSelectModal.vue';
const { cmbShowRender } = useColumnsRender();

const { productTypeOptions, getProductTypeOptions } = useMerchant()



const {
  page
} = useCommon()

const { getPCode } = usePCode();
const pCode = ref('');

// 币种
const currencyOptions = ref([]);

//单位
const unitOptions = ref([]);

// 格式化币种显示
const formatCurrency = (code) => {
  const option = currencyOptions.value.find(opt => opt.value === code);
  return option ? option.label : code;
};

const formatUnit = (code) => {
  const option = unitOptions.value.find(opt => opt.value === code);
  return option ? option.label : code;
};

const formatGoodsCategory = (goodsCategory) => {
  if (!goodsCategory) return '';
  return cmbShowRender(goodsCategory, productTypeOptions.value);
};

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }

  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};

// 获取列表后自动触发编辑器
const getList = async () => {
  try {
    const params = {
      headId: props.headId
    };
    window.majesty.httpUtil.postAction(`${ycCsApi.auxiliaryMaterials.bizIAuxMatForContractList.list}?page=${page.current}&limit=${page.pageSize}`,
      params
    ).then(res => {
      if (res.code === 200) {
        tableData.value = res.data || [];
        editableKeys.value = tableData.value.map(item => item.id);
        page.total = res.total;

        // 数据加载完成后，如果不是查看模式，触发编辑器
        if (props.editConfig.editStatus !== editStatus.SHOW) {
          setTimeout(() => {
            triggerEditor();
          }, 200);
        }
      } else {
        message.error(res.message || '获取数据失败');
      }
    })
  } catch (error) {
    message.error('获取数据失败');
  }
};

const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

const emit = defineEmits(['update:value', 'change']);

const deleteLoading = ref(false)

const tableRef = ref();
const selectedRowKeys = ref([]);
const tableData = ref([]);
const dataSource = ref([]);
const editableKeys = ref([]);

// 商品选择弹窗状态
const productModalVisible = ref(false);

// 监听headId变化
watch(() => props.headId, (newVal) => {
  if (newVal) {
    getList();
    initIListSumData()
  }
}, { immediate: true });

const onPageChange = async (pageNumber, pageSize) => {

  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  await getList()
}




/* 新增数据 */
const handleAdd = () => {
  productModalVisible.value = true;
};

/* 处理商品选择 */
const handleProductSelect = async (selectedProducts) => {
  try {
    // 批量添加选中的商品到合同明细
    const addPromises = selectedProducts.map(product => {
      const contractDetail = {
        headId: props.headId,
        productName: product.productName,
        productModel: product.productModel,
        specification: product.specification,
        weight: product.weight,
        unit: product.unit,
        importUnit: product.importUnit,
        // 初始化其他字段
        importQuantity: 0,
        quantity: 0,
        unitPrice: 0,
        amount: 0,
        deliveryDate: '',
        usdTotal: 0,
        productCategory: ''
      };
      return addContractList(contractDetail);
    });

    const results = await Promise.all(addPromises);
    const successCount = results.filter(res => res.code === 200).length;

    if (successCount === selectedProducts.length) {
      message.success(`成功添加 ${successCount} 个商品`);
    } else {
      message.warning(`添加完成，成功 ${successCount} 个，失败 ${selectedProducts.length - successCount} 个`);
    }

    // 刷新列表和汇总数据
    await getList();
    initIListSumData();
  } catch (error) {
    console.error('添加商品失败', error);
    message.error('添加商品失败，请重试');
  }
};

/* 删除数据 */
const handlerDelete = () => {
  if (selectedRowKeys.value.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteContractList(selectedRowKeys.value).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
          initIListSumData()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {
    },
  });
}

/* 定义汇总数据 */
const totalData = ref({
  importQuantity:0,
  quantity:0,
  amount:0,
  usdTotal:0,
})

/* 获取表体汇总数据 */
const initIListSumData = () => {
  const params = {
    headId: props.headId
  };
  getContractTotal(params).then(res => {
    if (res.code === 200) {
      totalData.value  = {...res.data}
    }else {
      message.error(res.message)
    }
  })
}

// 计算属性：根据编辑状态动态生成列配置
const getColumns = computed(() => {
  return columns;
});

// 监听编辑状态变化，当进入编辑状态时自动触发编辑器
watch(() => props.editConfig.editStatus, (newVal) => {
  if (newVal !== editStatus.SHOW && tableData.value.length > 0) {
    nextTick(() => {
      triggerEditor();
    });
  }
});

// 处理合同数量变更
const handleContractQuantityChange = async (record, newValue) => {
  if (newValue === record.quantity) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, quantity: newValue };
      const response = await updateContractList(record.id, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        await getList();
        initIListSumData();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};

const handleImportQuantityChange = async (record, newValue) => {
  if (newValue === record.importQuantity) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, importQuantity: newValue };
      const response = await updateContractList(record.id, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        await getList();
        initIListSumData();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
}

// 处理发货日期变更
const handleDeliveryDateChange = async (record, newValue) => {
  // 格式化日期为字符串进行比较
  const newDateStr = newValue
  const currentDateStr = record.deliveryDate;

  if (newDateStr === currentDateStr) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.id === record.id);
    if (rowIndex !== -1) {
      const updatedRecord = { ...record, deliveryDate: newDateStr };
      const response = await updateContractList(record.id, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        await getList();
        initIListSumData();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};

// 触发编辑器打开
const triggerEditor = () => {
  if (props.editConfig.editStatus !== editStatus.SHOW) {
    // 确保使用最新的表格数据
    dataSource.value = [...tableData.value];

    // 构建所有行的编辑配置
    const editConfigs = [];
    dataSource.value.forEach(row => {
      editConfigs.push({ columnKey: 'quantity', rowKey: row.id });
      editConfigs.push({ columnKey: 'importQuantity', rowKey: row.id });
      editConfigs.push({ columnKey: 'deliveryDate', rowKey: row.id });
    });

    // 使用nextTick确保DOM已更新
    nextTick(() => {
      console.log('触发编辑器，行数:', dataSource.value.length, '编辑配置:', editConfigs);
      if (editConfigs.length > 0) {
        // 一次性打开所有单元格的编辑状态
        tableRef.value?.openEditor(editConfigs);
      }
    });
  }
};

// 页面加载完成后自动触发编辑状态
onMounted(() => {
  // 获取币种代码
  getPCode().then(res => {
    console.log('res', res)
    pCode.value = res;
    currencyOptions.value = Object.entries(pCode.value.CURR_OUTDATED).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
    unitOptions.value = Object.entries(pCode.value.UNIT).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })
  getProductTypeOptions()
});

// 初始化列定义
const columns = [
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 150,
    maxLength: 80,
    key: 'productName',
  },
  {
    title: '进口数量',
    dataIndex: 'importQuantity',
    width: 120,
    key: 'importQuantity',
    editable: 'cellEditorSlot',
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '进口计量单位',
    dataIndex: 'importUnit',
    width: 120,
    key: 'importUnit',
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 120,
    editable: 'cellEditorSlot',
    key: 'quantity',
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 100,
    key: 'unit',
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    width: 120,
    editable: 'cellEditorSlot',
    key: 'unitPrice',
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 120,
    key: 'amount',
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '发货日期',
    dataIndex: 'deliveryDate',
    width: 120,
    editable: 'cellEditorSlot',
    key: 'deliveryDate',
  },
  {
    title: '总值折美元',
    dataIndex: 'usdTotal',
    width: 120,
    key: 'usdTotal',
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '规格',
    dataIndex: 'specification',
    width: 150,
    key: 'specification',
  },
  {
    title: '商品类别',
    dataIndex: 'productCategory',
    width: 150,
    editable: 'cellEditorSlot',
    key: 'productCategory',
  },
];


const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

defineExpose({
  getTableData: () => tableData.value,
  reloadData: getList // 暴露刷新方法供父组件调用
});
</script>

<style scoped>
.contract-detail-table {
  margin-top: 16px;
}

</style>
