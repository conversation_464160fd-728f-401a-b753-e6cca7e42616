<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <cost-head-search ref="headSearch" :typeMessage="typeMessage"/>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:costIManage:add']">
            <a-button size="small" @click="handlerAdd" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:costIManage:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:costIManage:chargeback']">
            <a-button  size="small" :loading="chargebackLoading" @click="handlerChargeback">
              <template #icon>
                <GlobalIcon type="swap-left" style="color:orange"/>
              </template>
              退单
            </a-button>
          </div>
          <div class="cs-action-btn-item"  v-has="['yc-cs:costIManage:affirm']">
            <a-button  size="small"  :loading="affirmLoading" @click="handlerAffirm">
              <template #icon>
                <GlobalIcon type="check" style="color:deepskyblue"/>
              </template>
              确认
            </a-button>
          </div>
<!--        <div class="cs-action-btn-item"  v-has="['yc-cs:costIManage:copy']">-->
<!--          <a-button  size="small"  :loading="copyLoading" @click="handlerCopy">-->
<!--            <template #icon>-->
<!--              <GlobalIcon type="snippets" style="color:deepskyblue"/>-->
<!--            </template>-->
<!--            {{localeContent('m.common.button.copy')}}-->
<!--          </a-button>-->
<!--        </div>-->
        <div class="cs-action-btn-item"  v-has="['yc-cs:costIManage:cancellation']">
          <a-button  size="small"  :loading="cancellationLoading" @click="handlerCancellation">
            <template #icon>
              <GlobalIcon type="close-square" style="color:deepskyblue"/>
            </template>
            作废
          </a-button>
        </div>
        <div class="cs-action-btn-item"  v-has="['yc-cs:costIManage:export']">
          <a-button  size="small"  :loading="exportLoading" @click="handlerExport">
            <template #icon>
              <GlobalIcon type="cloud-download" style="color:deepskyblue"/>
            </template>
            {{localeContent('m.common.button.export')}}
          </a-button>
        </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>


      </div>

      <!-- 表格区域 -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          v-if="showColumns.length > 0"
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>

              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination           v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <div v-if="!show">
      <cost-message-tab :editConfig="editConfig" @onEditBack="handlerOnBack" :typeMessage="typeMessage"/>
    </div>

  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, reactive, ref, watch} from "vue";
import {getColumns} from "@/view/costManage/costIManage/CostHeadColumns";
import CostHeadSearch from "@/view/costManage/costIManage/CostHeadSearch.vue";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {deleteCostHead,costIHeadChargeback,costIHeadAffirm,costIHeadcancellation,costIHeadcanCopy} from "@/api/cost/cost_message_info";
import CostMessageTab from "./CostMessageTab.vue"
import {localeContent} from "../../utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
const { importConfig } = useImport()


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()



defineOptions({
  name: 'CostHeadMessage',
});



const importShow = ref(false)



onMounted(fn => {


  ajaxUrl.selectAllPage = ycCsApi.costI.list
  ajaxUrl.exportUrl = ycCsApi.costI.export

  tableHeight.value = getTableScroll(100,'');

  getList()
  initCustomColumn()
  getCostOptions();
  getBuyerOptions();
  getCurrOptions();
  getProductTypeOptions();
})

const tableHeight = ref('')

const typeMessage = ref({
  //基础资料-客商信息
   buyerOptions: [],
  //基础资料-费用类型
   costTypeOptions: [],
  //币种
   currTypeOptions: [],
  //商品类别
  productTypeOptions: [],
  //费用客商
  costKTypeOptions:[]
})
const { totalColumns } = getColumns(typeMessage)
const getCostOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.costType.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        console.log(item.commonFlag)
        if(item.commonFlag.includes("1")){
          console.log("执行",item.commonFlag)
          typeMessage.value.costTypeOptions.push({
            value: item.paramCode,
            label: item.costName
          });
          typeMessage.value.costKTypeOptions.push({
            value: item.paramCode,
            label: item.customerSupplier
          });
        }
      });
    } else {
      message.error(res.message || '获取费用类型数据失败');
    }
  } catch (error) {
    message.error('获取费用类型数据失败');
  }
}

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        typeMessage.value.buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

const getCurrOptions = async () => {
  try {
    const params = {paramsType: "CURR"}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.baseInfoCustomerParams.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
          typeMessage.value.currTypeOptions.push({
            value: item.paramsCode,
            label: item.customParamCode
          });
      });
    } else {
      message.error(res.message || '获取币种类型数据失败');
    }
  } catch (error) {
    message.error('获取币种类型数据失败');
  }
}

const getProductTypeOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.params.productType.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        typeMessage.value.productTypeOptions.push({
          value: item.categoryCode,
          label: item.categoryName
        });
      });
    } else {
      message.error(res.message || '获取商品类别数据失败');
    }
  } catch (error) {
    message.error('获取商品类别数据失败');
  }
}
/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)

const chargebackLoading = ref(false)
const affirmLoading = ref(false)
const copyLoading = ref(false)
const cancellationLoading = ref(false)
const exportLoading = ref(false)

/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  if (flag){
    getList()
  }
}

/* 新增数据 */
const handlerAdd = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  show.value = !show.value;
}


/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]

  console.log('editConfig.value.editData', gridData)
  show.value =!show.value;
}
const handlerExport = () => {
  const now = new Date();
  // Format: YYYYMMDDHHmmss
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;

  doExport('进口费用_'+timestamp+'.xlsx',totalColumns)
}
const handlerCancellation = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  //弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '作废',
    cancelText: '取消',
    content: '确认作废所选项吗？',
    onOk() {
      cancellationLoading.value = true
      costIHeadcancellation(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("作废成功！")
          getList()
        }
      }).finally(() => {
        cancellationLoading.value = false
      })
    },
    onCancel() {

    },
  });
}
const handlerCopy = () => {
  if (gridData.selectedRowKeys.length <= 0 && gridData.selectedRowKeys.length !== 1){
    message.warning('请选择一条数据')
    return
  }
  //弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '是否复制所选项？',
    cancelText: '取消',
    content: '确认复制所选项吗？',
    onOk() {
      handlerCopy.value = true
      costIHeadcanCopy(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("复制成功！")
          getList()
        }
      }).finally(() => {
        handlerCopy.value = false
      })
    },
    onCancel() {

    },
  });
}
const handlerAffirm = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  var state = true
  //判断业务状态是否有为1的
  gridData.selectedData.forEach(item => {
    if(item.state === '1'){
      message.warning('选择数据中存在已确认的状态！')
      if(state){
       state = false
      }
    }
  })
  if(state){
    // 弹出确认框
    Modal.confirm({
      title: '提醒',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: '确认所选项吗？',
      onOk() {
        affirmLoading.value = true
        costIHeadAffirm(gridData.selectedRowKeys).then(res => {
          if (res.code === 200) {
            message.success("确认成功！")
            getList()
          }
        }).finally(() => {
          affirmLoading.value = false
        })
      },
      onCancel() {

      },
    });
  }
}

const handlerChargeback = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  //弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '退单',
    cancelText: '取消',
    content: '是否退单所选项？',
    onOk() {
      chargebackLoading.value = true
      costIHeadChargeback(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("退单成功！")
          getList()
        }
      }).finally(() => {
        chargebackLoading.value = false
      })
    },
    onCancel() {

    },
  });
}

/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  for (let selectedDatum of gridData.selectedData) {
    console.log(selectedDatum)
    if(selectedDatum.state !== '0'){
      message.warning('仅编制状态数据允许删除！')
      return
    }
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '所选中的明细中存在相同合同或进货单号，将一起删除，请确认？',
    onOk() {
      deleteLoading.value = true
      deleteCostHead(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}

/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router',window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.patch : useRoute().path
const originalColumns = ref()




/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}



/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
  //  深拷贝之前实现 丢失了方法，所以修改了utils的deepClone方法，这里不要了
  // showColumns.value.map((item) => {
  //   let temp = totalColumns.value.find((tempItem) => tempItem.key === item.key);
  //   if (temp && temp.customRender) {
  //     item.customRender = temp.customRender;
  //   }
  // });
}





/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})






</script>

<style lang="less" scoped>


</style>
