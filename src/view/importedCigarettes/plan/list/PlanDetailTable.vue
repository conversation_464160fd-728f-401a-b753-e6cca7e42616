<template>
  <div class="plan-detail-table">

    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-plan-list:add']">
        <a-button size="small" :loading="addLoading" @click="showProductSelector" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="plus" style="color:green"/>
          </template>
          {{ localeContent('m.common.button.add') }}
        </a-button>
      </div>

      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-plan-list:edit']">
        <a-button size="small" :loading="editLoading" @click="handlerEdit" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="form" style="color:orange"/>
          </template>
          {{ localeContent('m.common.button.update') }}
        </a-button>
      </div>

      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-plan-list:delete']">
        <a-button size="small" :loading="deleteLoading" @click="handlerDelete" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="delete" style="color:red"/>
          </template>
          {{ localeContent('m.common.button.delete') }}
        </a-button>
      </div>

      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-plan-list:import']">
        <a-button size="small" :loading="importLoading" @click="handlerImport" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="file-excel" style="color:deepskyblue"/>
          </template>
          {{ localeContent('m.common.button.import') }}
        </a-button>
      </div>
    </div>
    <s-table
      ref="tableRef"
      class="cs-action-item-modal-table remove-table-border-add-bg"
      bordered
      :custom-row="customRow"
      :data-source="tableData"
      :columns="getColumns"
      row-key="sid"
      :pagination="false"
      column-drag
      :loading="tableLoading"
      @blur="handleBlur"
      @keydown.esc="handleBlur"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <!-- 编辑状态的输入字段 -->
        <template v-if="typeof column.editable === 'function' ? column.editable(record) : column.editable">
          <!-- 商品名称字段使用下拉框 -->
          <template v-if="column.dataIndex === 'productName'">
            <a-select
              v-model:value="editableData[record.sid][column.dataIndex]"
              style="width: 100%"
              :options="productNames"
              @change="value => handleProductNameChange(record.sid, value)"
            ></a-select>
          </template>
          <!-- 币种字段使用下拉框 -->
          <template v-else-if="column.dataIndex === 'curr'">
            <a-select
              v-model:value="editableData[record.sid][column.dataIndex]"
              style="width: 100%"
              :options="currencyOptions"
            ></a-select>
          </template>
          <template v-else-if="column.dataIndex === 'supplier'">
            <a-select
              v-model:value="editableData[record.sid][column.dataIndex]"
              style="width: 100%"
              :options="merchantOptions"
            ></a-select>
          </template>
          <!-- 折扣率字段使用带百分号的数字输入框 -->
          <template v-else-if="column.dataIndex === 'discountRate'">
            <a-input-number
              :value="editableData[record.sid][column.dataIndex]"
              style="width: 100%"
              :formatter="value => `${value}%`"
              :parser="value => value.replace('%', '')"
              @change="value => {
                editableData[record.sid][column.dataIndex] = value;
                inputTotal(record.sid);
              }"
              :data-field="column.dataIndex"
            />
          </template>
          <!-- 数值字段使用数字输入框 -->
          <template v-else-if="['planQuantity', 'unitPrice', 'totalAmount', 'discountAmount'].includes(column.dataIndex)">
            <a-input-number
              :value="editableData[record.sid][column.dataIndex]"
              style="width: 100%"
              :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              @change="value => {
                editableData[record.sid][column.dataIndex] = value;
                inputTotal(record.sid);
              }"
              :data-field="column.dataIndex"
            />
          </template>
          <!-- 其他字段使用普通输入框 -->
          <template v-else>
            <a-input
              :value="editableData[record.sid][column.dataIndex]"
              @change="e => editableData[record.sid][column.dataIndex] = e.target.value"
              @input="inputTotal(record.sid)"
            />
          </template>
        </template>

        <!-- 非编辑状态的各种字段渲染 -->
        <template v-else>
          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'operation' && props.editConfig.editStatus !== editStatus.SHOW">
            <div class="editable-row-operations">
              <span v-if="checkIsEditing(record)">
                <a-typography-link @click="save(record.sid)">保存</a-typography-link>
                <a-divider type="vertical" />
                <a @click="cancel(record.sid)">取消</a>
              </span>
              <span v-else>
                <a @click="edit(record.sid)">编辑</a>
              </span>
            </div>
          </template>

          <!-- 折扣率字段 -->
          <template v-else-if="column.dataIndex === 'discountRate'">
            {{record[column.dataIndex]}} %
          </template>
          <template v-else-if="column.dataIndex === 'curr'">
            {{record[column.dataIndex]}}
            <!--            {{ formatCurrency(record[column.dataIndex]) }}-->
          </template>
          <!-- 供应商字段显示 -->
          <template v-else-if="column.dataIndex === 'supplier'">
            {{ formatSupplier(record[column.dataIndex]) }}
          </template>
          <!-- 数值字段格式化显示 -->
          <template v-else-if="['planQuantity', 'unitPrice', 'totalAmount', 'discountAmount'].includes(column.dataIndex)">
            {{ formatNumber(record[column.dataIndex]) }}
          </template>
          <!-- 其他普通字段 -->
          <template v-else>
            {{ record[column.dataIndex] }}
          </template>
        </template>
      </template>
      <template #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer }">
        <template v-if="column.dataIndex === 'curr'">
          <a-select
            :ref="editorRef"
            :bordered="false"
            :value="modelValue.value"
            style="width: 160px"
            :get-popup-container="getPopupContainer"
            :options="currencyOptions"
            :open="true"
            :auto-focus="true"
            @popup-visible-change="visible => !visible && closeEditor()"
            @update:value="v => {
              modelValue.value = v;
              save();
            }"
            @blur="closeEditor"
            @keydown.esc="closeEditor"
          ></a-select>
        </template>
      </template>
    </s-table>

    <!-- 添加商品选择弹窗 -->
    <a-modal
      v-model:visible="productSelectorVisible"
      title="选择物料信息"
      width="800px"
      okText="保存"
      cancelText="返回"
      @ok="handleSelectProducts"
      @cancel="productSelectorVisible = false"
    >
      <a-spin :spinning="productLoading">
        <!-- 添加查询区域 -->
        <div style="margin-bottom: 16px; display: flex; gap: 8px; align-items: center;">
          <span style="margin-right: 8px;">商品名称：</span>
          <a-input
            v-model:value="productNameKeyword"
            placeholder="请输入商品名称"
            style="width: 200px;"
            @keyup.enter="handleSearchProducts"
          />
          <a-button type="primary" @click="handleSearchProducts">查询</a-button>
        </div>
        <a-table
          :columns="productColumns"
          :data-source="productList"
          :pagination="false"
          :scroll="{ y: 400 }"
          :row-selection="{
            selectedRowKeys: selectedProductKeys,
            onChange: onProductSelectChange,
            type: 'checkbox',
            preserveSelectedRowKeys: false
          }"
          :row-key="record => record.sid || record.productCode || Date.now() + Math.random()"
        >
        </a-table>
      </a-spin>
    </a-modal>


    <!-- 导入数据 -->
    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>

    <!-- 分页 -->
    <div class="cs-pagination">
      <div class="cs-list-total-data">
        计划总数量：{{formatNumber(totalData.qtyTotal)}} ，计划总金额：{{formatNumber(totalData.decTotal)}}
      </div>
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, watch, onMounted, createVNode, computed, h, nextTick} from 'vue';

// 添加格式化数字的辅助函数
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '';
  }

  // 分别处理整数部分和小数部分
  const parts = num.toFixed(2).split('.');
  // 只对整数部分添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  // 组合整数和小数部分
  return parts.join('.');
};
import {message, Modal, Tag} from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {deletePlanList, updatePlanList, insertPlanList} from "@/api/importedCigarettes/plan/planApi";
import { usePCode } from "@/view/common/usePCode";
import {localeContent} from "@/view/utils/commonUtil";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {useCommon} from '@/view/common/useCommon'
import {editStatus,productClassify} from "@/view/common/constant";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
import {updateBizErpIOrderList} from "@/api/cs_api_constant";

/* 导入 */
import {ImportIndex} from 'yao-import'
import { useImport } from "@/view/common/useImport"
let { importConfig } = useImport()
const importShow = ref(false)

const {
  page,
  tableLoading
} = useCommon()

const { getPCode } = usePCode();
const pCode = ref('');
const detailEditStatus=ref(editStatus.SHOW);
// 币制
const currencyOptions = ref([]);

// 格式化币制显示
const formatCurrency = (code) => {
  const option = currencyOptions.value.find(opt => opt.value === code);
  return option ? option.label : code;
};
// ... existing code ...
// 格式化供应商显示
const formatSupplier = (code) => {
  const option = merchantOptions.value.find(opt => opt.value === code);
  return option ? `${code} ${option.label}` : code;
};
// ... existing code ...
const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
// 获取币制代码
onMounted(() => {
  getPCode().then(res => {
    pCode.value = res;
    currencyOptions.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      // label: `${value} ${label}`,
      value
    }));
  });
  loadProducts('')
  getMerchantOptions()

  if (window.fuyun){
    window.fuyun.majesty.util.handleAsyncImport({id:'1913125954041937922',arrangeId:'1913141292251041794'}).then(res=>{
      importConfig = res
      importConfig.businessParam = {
        headId: props.headId
      }
    })
  }

  // 如果是通过列表编辑按钮进入，设置所有行为可编辑状态
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    detailEditStatus.value = editStatus.EDIT;
  }
})



const emit = defineEmits(['update:value', 'change']);

const { merchantOptions, getMerchantOptions } = useMerchant()

const deleteLoading = ref(false);
const addLoading = ref(false); // 新增按钮 loading
const editLoading = ref(false); // 编辑按钮 loading
const importLoading = ref(false); // 导入按钮 loading
const { cmbShowRender } = useColumnsRender()

const tableRef = ref();
const selectedRowKeys = ref([]);
const tableData = ref([]);
const editableKeys = ref([]);

// 添加汇总数据对象
const totalData = ref({
  qtyTotal: 0,
  decTotal: 0
});

// 添加一个状态变量，用于跟踪新增行的 sid
const newRowSid = ref(null);

// 添加一个新的数组来跟踪新增的行
const newAddedRows = ref([]);

// 添加 editableData 对象来跟踪编辑状态
const editableData = reactive({});

// 添加商品选择相关变量
const productSelectorVisible = ref(false);
const productList = ref([]);
const productLoading = ref(false);
const productNames = ref([]);
const selectedProductKeys = ref([]);
const selectedProducts = ref([]);

// 添加商品名称查询相关变量
const productNameKeyword = ref('');

// 供应商列表相关变量
const supplierOptions = ref([]);

// 商品列表的列定义
const productColumns = [
  {
    title: '商品名称',
    dataIndex: 'gName',
    key: 'gName',
    resizable:"true",
    width: 180
  },
  {
    title: '供应商',
    dataIndex: 'supplierCode',
    key: 'supplierCode',
    resizable:"true",
    width: 200,
    // 自定义渲染，合并显示供应商代码和名称
    customRender: ({ text, record }) => {
      return record.supplierCode && record.merchantNameCn
        ? `${record.supplierCode} ${record.merchantNameCn}`
        : text;
    }
  },
  // {
  //   title: '启用状态',
  //   dataIndex: 'dataState',
  //   key: 'dataState',
  //   resizable:"true",
  //   width: 180,
  //   customRender: ({ text }) => {
  //     const tagColor = text === '1' ? 'error' : 'success';
  //     return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.dataStatus))
  //   }
  // },
];

// 监听headId变化
watch(() => props.headId, (newVal) => {
  if (newVal) {
    getList();
  }
}, { immediate: true });

// 监听编辑状态变化
watch(() => props.editConfig.editStatus, (newVal) => {
  // 如果是通过列表编辑按钮进入，设置所有行为可编辑状态
  if (newVal === editStatus.EDIT) {
    detailEditStatus.value = editStatus.EDIT;
    // 在数据加载完成后，将所有行设置为编辑状态
    nextTick(() => {
      if (tableData.value && tableData.value.length > 0) {
        tableData.value.forEach(record => {
          // 只对没有下游数据关联的行设置编辑状态
          if (record.hasCtr !== '1') {
            editableData[record.sid] = JSON.parse(JSON.stringify(record));
          }
        });
      }
    });
  }
});

// 获取列表数据
const getList = async () => {
  tableLoading.value = true
  try {
    const params = {
      headId: props.headId
    };
    window.majesty.httpUtil.postAction(`${ycCsApi.importedCigarettes.planList.list}?page=${page.current}&limit=${page.pageSize}`,
      params
    ).then(res => {
      if (res.code === 200) {
        tableData.value = res.data || [];
        editableKeys.value = tableData.value.map(item => item.sid);
        page.total = res.total || 0;
        // 计算汇总数据
        calculateTotals();

        // 如果是通过列表编辑按钮进入，设置所有行为可编辑状态
        if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
          // 将所有没有下游数据关联的行设置为编辑状态
          tableData.value.forEach(record => {
            if (record.hasCtr !== '1') {
              editableData[record.sid] = JSON.parse(JSON.stringify(record));
              // 确保计划数量不为0，如果是0则设为空字符串
              if (editableData[record.sid].planQuantity === 0) {
                editableData[record.sid].planQuantity = '';
              }
            }
          });
        }
      } else {
        message.error(res.message || '获取数据失败');
      }
    }).finally(() => {
      tableLoading.value = false
    })
  } catch (error) {
    message.error('获取数据失败');
  }
};

// 处理分页变化
const onPageChange = async (pageNumber, pageSize) => {
  page.current = pageNumber;
  page.pageSize = pageSize;
  await getList();
};

// 计算汇总数据
const calculateTotals = () => {
  let qtyTotal = 0;
  let decTotal = 0;

  tableData.value.forEach(item => {
    // 计划数量汇总
    if (item.planQuantity) {
      const qty = parseFloat(item.planQuantity);
      if (!isNaN(qty)) {
        qtyTotal += qty;
      }
    }

    // 计划总金额汇总
    if (item.totalAmount) {
      const amount = parseFloat(item.totalAmount);
      if (!isNaN(amount)) {
        decTotal += amount;
      }
    }
  });

  totalData.value = {
    qtyTotal: qtyTotal,
    decTotal: decTotal
  };
};

// 处理表格数据变化
// const handleTableChange = (value, key, column) => {
//   emit('change', tableData.value);
// };

/**
 * 单元格编辑保存
 * @param res
 * @returns {Promise<*>}
 */
const handleCellEdit = async (record, dataIndex, newValue) => {
  if (newValue === record[dataIndex]) {
    return;
  }

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.sid === record.sid);
    if (rowIndex !== -1) {
      tableData.value[rowIndex][dataIndex] = newValue;

      // 这里调用您的后端API
      const response = await updatePlanList(record.sid, { ...record, [dataIndex]: newValue });
      if (response.code === 200) {
        // message.success('修改成功!');
        return newValue;
      } else {
        message.error(response.message || '修改失败');
        return record[dataIndex];
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
    return record[dataIndex];
  }
};
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    }, style: {cursor: 'pointer'}
    // 移除 onclick 事件，取消点击行时选中行的功能
    // 保留复选框选择功能
  };
};
const handleRowEnter = (record) => {
  // 如果行不可编辑，直接返回
  if (!checkIsEditing(record)) {
    return;
  }

  // 更新选中状态
  selectedRowKeys.value = [record.sid];

  // 设置编辑状态
  detailEditStatus.value = editStatus.ADD;

  // 开始编辑该行
  edit(record);
};
const handleRowDblclick = (record) => {
  if(props.editConfig.editStatus !== editStatus.SHOW){
    // 检查是否有下游数据关联
    if (record.hasCtr === '1') {
      message.warning('所选数据已产生后续单据，请将后续单据作废后再进行编辑修改');
      return;
    }
    edit(record.sid);
  }
};
const handleBlur = () => {
  const sid = selectedRowKeys.value[0];
  cancel(sid)
  getList()
};
const handleEnter = () => {
  const sid = selectedRowKeys.value[0];
  save(sid);
};

// 编辑单行数据
const edit = (sid) => {
  // 找到要编辑的记录
  const targetRecord = tableData.value.find(item => item.sid === sid);
  if (targetRecord) {
    // 检查是否有下游数据关联
    if (targetRecord.hasCtr === '1') {
      message.warning('所选数据已产生后续单据，请将后续单据作废后再进行编辑修改');
      return;
    }

    // 将该行设为选中状态
    // selectedRowKeys.value = [sid];

    // 深拷贝记录到编辑数据
    editableData[sid] = JSON.parse(JSON.stringify(targetRecord));

    // 确保计划数量不为0，如果是0则设为空字符串
    if (editableData[sid].planQuantity === 0) {
      editableData[sid].planQuantity = '';
    }

    // 设置编辑状态
    detailEditStatus.value = editStatus.EDIT;

    // 使用setTimeout确保DOM更新后再设置焦点
    setTimeout(() => {
      // 找到该行的计划数量输入框并设置焦点
      const rowInput = document.querySelector(`[data-row-key="${sid}"] input[data-field="planQuantity"]`);
      if (rowInput) {
        rowInput.focus();
      }
    }, 200); // 增加延迟时间，确保DOM完全更新
  }
};
const inputTotal = (sid) => {
  if(editableData[sid].unitPrice !== null && editableData[sid].planQuantity !== null && editableData[sid].planQuantity !== '' && editableData[sid].planQuantity !== undefined){
    editableData[sid].totalAmount = totalCount(editableData[sid])
  }
  if(editableData[sid].planQuantity === undefined || editableData[sid].planQuantity === null || editableData[sid].planQuantity === ''){
    editableData[sid].totalAmount = 0
  }
};
const totalCount = (row) => {
  const planQuantity = parseFloat(row.planQuantity);
  const unitPrice = parseFloat(row.unitPrice);

  // 检查是否为有效数字，避免NaN结果
  if (isNaN(planQuantity) || isNaN(unitPrice)) {
    return 0;
  }

  const total = roundToDecimal(planQuantity*unitPrice,5)
  return total !== null ? total : 0
};
function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

// 保存编辑的数据
const save = (sid) => {
  if (!editableData[sid]) return;

  const record = tableData.value.find(item => item.sid === sid);
  if (!record) return;

  // 检查表头数据是否已保存
  if (!props.headId) {
    message.warning('请先保存表头数据');
    return;
  }

  // 检查是否为新增行
  const isNewRow = record.isNewAdded === true ||
    newAddedRows.value.includes(sid) ||
    sid === newRowSid.value;

  // 创建更新/新增的数据对象
  const saveData = { ...record };

  // 只更新允许编辑的字段
  const editableFields = ['productName','supplier','englishBrand','origin','curr', 'unitPrice', 'unit','totalAmount','discountRate','discountAmount','planQuantity'];
  editableFields.forEach(field => {
    if (editableData[sid] && editableData[sid][field] !== undefined) {
      saveData[field] = editableData[sid][field];
    }
  });

  // 确保 headId 字段存在
  saveData.headId = props.headId;

  // 根据是否为新增行，调用不同的 API
  const apiPromise = isNewRow
    ? insertPlanList(saveData) // 新增记录
    : updatePlanList(sid, saveData); // 更新记录

  apiPromise.then((res) => {
    if (res.code === 200) {
      message.success(isNewRow ? '添加成功!' : '更新成功!');

      // 如果是新增行并且后端返回了新的 sid
      if (isNewRow && res.data && res.data.sid) {
        const index = tableData.value.findIndex(item => item.sid === sid);
        if (index !== -1) {
          const newSid = res.data.sid;

          // 保存原始记录（用于保持本地状态）
          const updatedRecord = {
            ...res.data,
            sid: newSid
          };

          // 重要：移除 isNewAdded 标记
          delete updatedRecord.isNewAdded;

          // 更新当前记录
          tableData.value[index] = updatedRecord;

          // 从临时新增标记中移除
          newAddedRows.value = newAddedRows.value.filter(id => id !== sid);

          // 如果是通过常规新增按钮添加的行
          if (sid === newRowSid.value) {
            newRowSid.value = null;
          }
        }
      } else {
        // 只更新当前行数据，不刷新整个列表
        const index = tableData.value.findIndex(item => item.sid === sid);
        if (index !== -1) {
          // 更新本地数据中的字段
          editableFields.forEach(field => {
            if (saveData[field] !== undefined) {
              tableData.value[index][field] = saveData[field];
            }
          });

          // 如果是更新行，还可能更新其他后端返回的字段
          if (!isNewRow && res.data) {
            Object.keys(res.data).forEach(key => {
              tableData.value[index][key] = res.data[key];
            });
          }
        }
      }

      // 清除此行的编辑状态
      delete editableData[sid];

      // 重要：从所有可能的新增标记中移除该行
      if (isNewRow) {
        const index = tableData.value.findIndex(item => item.sid === (res.data?.sid || sid));
        if (index !== -1) {
          // 移除 isNewAdded 标记
          delete tableData.value[index].isNewAdded;
        }
      }

      // 强制更新表格数据，确保视图重新渲染
      tableData.value = [...tableData.value];

      // 重新计算汇总数据
      calculateTotals();

      // 检查是否还有其他行在编辑中
      const stillEditing = Object.keys(editableData).length > 0;

      // 只有当没有任何行在编辑中时，才重置整体编辑状态
      if (!stillEditing) {
        detailEditStatus.value = editStatus.SHOW;
      }
    } else {
      message.error(res.message || '保存失败');
    }
  }).finally(() => {
    if(detailEditStatus.value !== editStatus.EDIT && detailEditStatus.value !== editStatus.ADD){
      getList()
    }
  }).catch((error) => {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  });
};

// 取消编辑 - 彻底清除选中状态
const cancel = (sid) => {
  // 先删除编辑状态
  delete editableData[sid];

  // 获取该行记录
  const record = tableData.value.find(item => item.sid === sid);

  // 检查是否为新增行
  const isNewRow = record?.isNewAdded === true ||
    newAddedRows.value.includes(sid) ||
    sid === newRowSid.value;

  if (isNewRow) {
    // 从数据源中移除
    tableData.value = tableData.value.filter(item => item.sid !== sid);

    // 从新增行数组中移除
    newAddedRows.value = newAddedRows.value.filter(id => id !== sid);

    // 如果是通过常规新增按钮添加的行
    if (sid === newRowSid.value) {
      newRowSid.value = null;
    }

  }

  // 更新表格数据，确保视图更新
  tableData.value = [...tableData.value];

  // 从选中行中移除
  if (selectedRowKeys.value.includes(sid)) {
    selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== sid);
  }

  // 检查是否还有编辑中的行
  const stillEditing = Object.keys(editableData).length > 0;

  // 如果没有编辑中的行，重置编辑状态
  if (!stillEditing) {
    detailEditStatus.value = editStatus.SHOW;
  }

};

const handlerAdd = async () => {
  addLoading.value = true; // 开始 loading
  const sid = Date.now().toString();

  // 设置新记录的默认值
  const newRecord = {
    sid: sid,
    productName: '',
    supplier: '',
    englishBrand: '',
    origin: '',
    planQuantity: '',
    unit: '万支',
    curr: 'USD',
    unitPrice: 0,
    totalAmount: 0,
    discountRate: 0,
    discountAmount: 0,
  };

  // 保存新增行的 sid
  newRowSid.value = sid;
  detailEditStatus.value = editStatus.ADD;

  // 使用 Vue 的响应式方法更新 tableData
  tableData.value.push(newRecord);
  // 触发视图更新
  tableData.value = [...tableData.value];

  // 设置为编辑状态
  editableData[sid] = JSON.parse(JSON.stringify(newRecord));
  if(editableData[sid].unitPrice !== null && editableData[sid].planQuantity !== null){
    editableData[sid].totalAmount = totalCount(editableData[sid])
  }

  // 确保表格滚动到新行位置
  setTimeout(() => {
    const table = tableRef.value;
    if (table && table.scrollTo) {
      table.scrollTo({
        top: table.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, 100);

  addLoading.value = false; // 结束 loading
};

const handlerEdit = () => {
  if (selectedRowKeys.value.length !== 1) {
    message.warning('请选择一条数据进行编辑');
    return;
  }

  const sid = selectedRowKeys.value[0];

  // 检查选中记录是否有下游数据关联
  const targetRecord = tableData.value.find(item => item.sid === sid);
  if (targetRecord && targetRecord.hasCtr === '1') {
    message.warning('所选数据已产生后续单据，请将后续单据作废后再进行编辑修改');
    return;
  }

  detailEditStatus.value = editStatus.EDIT;
  editLoading.value = true;

  // 设置编辑状态
  edit(sid);

  editLoading.value = false;
};

/* 删除数据 */
const handlerDelete = () => {
  if (selectedRowKeys.value.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deletePlanList(selectedRowKeys.value).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
          // 重新计算汇总数据
          calculateTotals();
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {
    },
  });
}

// 计算属性：根据编辑状态动态生成列配置
const getColumns = computed(() => {
  // 根据编辑状态决定是否显示操作列
  let columnsToShow = [...columns];

  // 如果是查看模式，则移除操作列
  // if (props.editConfig.editStatus === editStatus.SHOW) {
  columnsToShow = columnsToShow.filter(column => column.dataIndex !== 'operation');
  // }

  return columnsToShow.map(column => {
    return {
      ...column,
      editable: (record) => {
        // 如果有下游数据关联，禁止编辑
        if (record.hasCtr === '1') {
          return false;
        }

        // 如果是通过列表编辑按钮进入，所有字段都可编辑
        if (props.editConfig.editStatus === editStatus.EDIT) {
          return ['productName','englishBrand','origin','curr', 'unitPrice', 'unit','totalAmount','discountRate','discountAmount','planQuantity'].includes(column.dataIndex);
        }

        // 如果是新增状态且是新增的行，所有字段都可编辑
        if (detailEditStatus.value === editStatus.ADD && record.sid === newRowSid.value) {
          return true;
        }

        // 如果该记录在编辑状态中，只有特定字段可编辑
        if (editableData[record.sid]) {
          return ['productName','englishBrand','origin','curr', 'unitPrice', 'unit','totalAmount','discountRate','discountAmount','planQuantity'].includes(column.dataIndex);
        }

        // 否则不可编辑
        return false;
      }
    };
  });
});

// 初始化列定义
const columns = [
  {
    width: 120,
    minWidth:80,
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
    fixed: 'left',
    resizable:"true",
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 150,
    resizable:"true",
    maxLength: 80
  },
  {
    title: '供应商',
    width: 200,
    minWidth: 200,
    align: 'center',
    dataIndex: 'supplier',
    resizable: true,
    key: 'supplier',
    customRender: ({ text }) => {
      return h(Tag, cmbShowRender(text, merchantOptions.value))
    }
  },{
    title: '计划数量(数量)',
    dataIndex: 'planQuantity',
    resizable:"true",
    width: 120,
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '计划数量(单位)',
    dataIndex: 'unit',
    width: 120,
    resizable:"true",
    maxLength: 10
  },
  {
    title: '英文品牌',
    dataIndex: 'englishBrand',
    width: 100,
    resizable:"true",
    maxLength: 10
  },
  {
    title: '原产地',
    dataIndex: 'origin',
    width: 100,
    resizable:"true",
    maxLength: 10
  },

  {
    title: '币种',
    dataIndex: 'curr',
    width: 150,
    resizable:"true",
    key: 'curr',
    customRender: ({ text }) => {
      return h(Tag, cmbShowRender(text,[],'CURR'))
    }
  },
  {
    title: '计划金额(单价)',
    dataIndex: 'unitPrice',
    resizable:"true",
    width: 120,
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '计划总金额',
    dataIndex: 'totalAmount',
    resizable:"true",
    width: 100,
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
  {
    title: '折扣率%',
    dataIndex: 'discountRate',
    resizable:"true",
    width: 100,
    // 添加自定义渲染以显示带有"%"的折扣率
    customRender: ({ text }) => {
      return `${text}%`;
    }
  },
  {
    title: '折扣金额',
    dataIndex: 'discountAmount',
    resizable:"true",
    width: 100,
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
];

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 商品选择表格的选择事件
const onProductSelectChange = (keys, rows) => {

  // 使用新数组实例重置选中状态
  selectedProductKeys.value = [...keys];
  selectedProducts.value = [...rows];
};

// 打开商品选择器
const showProductSelector = () => {
  // 每次打开时完全重置选择状态
  selectedProductKeys.value = [];
  selectedProducts.value = [];
  productNameKeyword.value = ''; // 重置关键词

  productSelectorVisible.value = true;
};

// 处理商品查询
const handleSearchProducts = () => {
  // 调用加载商品数据接口，传入商品名称参数
  loadProducts(productNameKeyword.value);
};

// 排序函数：先按供应商排序，再按单价排序（升序）
// 类似于SQL中的 ORDER BY t.supplier, t.unit_price ASC
const sortTableDataBySupplierAndPrice = (data) => {
  // 自定义排序函数，模拟数据库的排序行为
  const customCompare = (a, b) => {
    // 如果其中一个是空，则空的排在后面
    if (!a) return 1;
    if (!b) return -1;

    // 检查是否为纯数字
    const isNumericA = /^\d+$/.test(a);
    const isNumericB = /^\d+$/.test(b);

    // 如果一个是纯数字，另一个不是，则数字在前
    if (isNumericA && !isNumericB) return -1;
    if (!isNumericA && isNumericB) return 1;

    // 如果两个都是纯数字，则按数字大小排序
    if (isNumericA && isNumericB) {
      return parseInt(a) - parseInt(b);
    }

    // 如果都不是纯数字，则按字符顺序排序
    // 大写字母应该排在小写字母前面
    // 先检查第一个字符是否为大写
    const isUpperA = a.charAt(0) === a.charAt(0).toUpperCase() && a.charAt(0) !== a.charAt(0).toLowerCase();
    const isUpperB = b.charAt(0) === b.charAt(0).toUpperCase() && b.charAt(0) !== b.charAt(0).toLowerCase();

    if (isUpperA && !isUpperB) return -1; // 大写在前
    if (!isUpperA && isUpperB) return 1; // 小写在后

    // 如果大小写状态相同，则按字母顺序排序
    return a.localeCompare(b);
  };

  return [...data].sort((a, b) => {
    // 首先按供应商代码排序
    const supplierA = a.supplier || '';
    const supplierB = b.supplier || '';

    // 如果供应商代码不同，则使用自定义排序函数
    if (supplierA !== supplierB) {
      return customCompare(supplierA, supplierB);
    }

    // 如果供应商相同，则按单价排序（数字升序）
    const priceA = parseFloat(a.unitPrice || 0);
    const priceB = parseFloat(b.unitPrice || 0);
    return priceA - priceB;
  });
};

// 加载商品数据 - 确保每条数据都有唯一的sid
const loadProducts = async (productName = '') => {
  productLoading.value = true;
  try {
    // 创建请求参数对象，包含商品名称
    const params = {
      gName: productName // 传递商品名称参数
    };

    // 调用API获取商品信息，传入参数
    const response = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMaterialInformation.matForPlan}`,
      params
    );

    if (response.code === 200) {
      // 确保每个商品项有唯一的 sid
      productList.value = (response.data || []).map(item => ({
        ...item,
        sid: item.sid || item.productCode || `mat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }));
      // 将 productNames 转换为对象数组
      productNames.value = productList.value.map(product => ({
        label: product.gName,
        value: product.gName
      }));
      // 重置选择状态
      selectedProductKeys.value = [];
      selectedProducts.value = [];
    } else {
      message.error(response.message || '获取商品信息失败');
    }
  } catch (error) {
    console.error('加载商品数据失败:', error);
    message.error('获取商品信息失败');
  } finally {
    productLoading.value = false;
  }
};

// 确认选择商品 - 修复重复提示的问题
const handleSelectProducts = () => {
  if (selectedProducts.value.length === 0) {
    message.warning('请至少选择一个商品');
    return;
  }
  const productNames = tableData.value.map(item => item.productName).filter(name => name !== undefined);
  var filter = selectedProducts.value.filter(item => productNames.includes(item.gName));
  if(filter.length > 0){
    message.warning('商品名称'+filter.map(item => item.gName).join(',')+'已存在！');
    return;
  }

  addLoading.value = true;
  let selectedSid = '';

  // 保存选中商品数量用于显示消息
  const addedCount = selectedProducts.value.length;

  // 保存当前表格中的所有数据（深拷贝以避免引用问题）
  const existingData = JSON.parse(JSON.stringify(tableData.value));

  // 创建新记录数组
  const newRecords = [];

  try {
    // 为每个选中的商品创建一行数据
    selectedProducts.value.forEach(product => {
      const sid = Date.now().toString() + Math.random().toString(36).substr(2, 5);

      // 将新行添加到跟踪数组中
      newAddedRows.value.push(sid);
      selectedSid = sid;

      // 从选中商品创建新记录，并明确标记为新增
      const newRecord = {
        sid: sid,
        productName: product.gName || '',
        supplier: product.supplierCode || '',
        supplierName: product.merchantNameCn || '',
        supplierDisplay: product.supplierCode && product.merchantNameCn
          ? `${product.supplierCode} ${product.merchantNameCn}`
          : product.supplierCode,
        englishBrand: product.fullEnName || '',
        origin: product.origin || '',
        planQuantity: '', // 确保计划数量为空字符串，而不是0
        unit: '万支',
        curr: 'USD',
        unitPrice: product.importUnitPrice|| '',
        totalAmount: 0,
        discountRate: formatDiscountRate(product.supplierDiscountRate),
        discountAmount: 0,
        productCode: product.productCode,
        headId: props.headId,
        isNewAdded: true // 明确标记为新增行
      };

      // 添加到新记录数组
      newRecords.push(newRecord);

      // 设置为可编辑状态
      editableData[sid] = JSON.parse(JSON.stringify(newRecord));
    });

    // 合并现有数据和新数据
    const combinedData = [...existingData, ...newRecords];

    // 对合并后的数据进行排序
    const sortedData = sortTableDataBySupplierAndPrice(combinedData);

    // 用排序后的数据替换表格数据
    tableData.value = sortedData;

    // 设置编辑状态
    detailEditStatus.value = editStatus.ADD;

    // 关闭弹窗
    productSelectorVisible.value = false;

    // 显示成功消息
    message.success(`成功添加 ${addedCount} 条商品记录`);

    // 确保表格滚动到底部
    setTimeout(() => {
      const table = tableRef.value;
      if (table && table.scrollTo) {
        table.scrollTo({
          top: table.scrollHeight,
          behavior: 'smooth'
        });
      }

      // 如果有添加的行，选中第一个新添加的行并设置焦点到计划数量输入框
      if (newRecords.length > 0) {
        // 找到第一个新添加的行
        const firstNewRecord = newRecords[0];
        const firstNewRecordInTable = tableData.value.find(item => item.sid === firstNewRecord.sid);

        if (firstNewRecordInTable) {
          // 开始编辑该行
          edit(firstNewRecordInTable.sid);

          // 使用setTimeout确保DOM更新后再设置焦点
          setTimeout(() => {
            // 找到该行的计划数量输入框并设置焦点
            const rowInput = document.querySelector(`[data-row-key="${firstNewRecordInTable.sid}"] input[data-field="planQuantity"]`);
            if (rowInput) {
              rowInput.focus();
            }
          }, 200); // 增加延迟时间，确保DOM完全更新
        }
      }
    }, 200); // 增加延迟时间，确保DOM完全更新
  } catch (error) {
    console.error('添加商品记录失败:', error);
    message.error('添加商品记录失败');
  } finally {
    addLoading.value = false;
  }
};

// 检查行是否处于编辑状态 - 修改以确保准确判断
const checkIsEditing = (record) => {
  // 如果有下游数据关联，始终返回false
  if (record.hasCtr === '1') {
    return false;
  }

  // 1. 检查是否在编辑数据中
  if (editableData[record.sid]) {
    return true;
  }

  // 2. 检查是否标记为新增行
  if (record.isNewAdded === true) {
    return true;
  }

  // 3. 检查是否在新增行数组中
  if (newAddedRows.value.includes(record.sid)) {
    return true;
  }

  // 4. 检查是否与 newRowSid 匹配
  if (record.sid === newRowSid.value) {
    return true;
  }

  return false;
};

// 添加一个方法来处理 productName 的变化
const handleProductNameChange = (sid, selectedProductName) => {
  const matchedProduct = productList.value.find(product => product.gName === selectedProductName);

  if (matchedProduct) {
    // 更新行中的其他字段
    editableData[sid].supplier = matchedProduct.supplierCode || '';
    editableData[sid].englishBrand = matchedProduct.fullEnName || '';
    editableData[sid].unitPrice = matchedProduct.importUnitPrice || 0;

    // 同时更新 tableData 中的值以确保非编辑状态下显示正确
    const record = tableData.value.find(item => item.sid === sid);
    if (record) {
      record.supplier = matchedProduct.supplierCode || '';
      record.englishBrand = matchedProduct.fullEnName || '';
      record.unitPrice = matchedProduct.importUnitPrice || 0;
    }

    // 强制更新 tableData 以触发视图重新渲染
    tableData.value = [...tableData.value];
  }
};

const formatDiscountRate = (value) => {
  if (!value) return '0';

  // 转换为数字并验证
  let num = Number(value);

  // 检查整数部分是否超过15位
  if (Math.floor(Math.abs(num)).toString().length > 15) {
    // 如果超过15位，截取前15位
    num = Number(Math.floor(Math.abs(num)).toString().slice(0, 15));
  }

  // 格式化为最多4位小数，并移除末尾的0
  return Number(num.toFixed(4)).toString();
};

/* 进口计划表体导入 */
const handlerImport = () => {
  // 判断表头是状态是否是 编制状态
  console.log('props',props.editConfig.editData.status)
  if(props.editConfig.editData.status !== '0'){
    message.warning('仅单据状态为编制的数据允许导入！')
    return
  }
  // 打开导入弹窗
  importShow.value = true;
}
const importSuccess = ()=>{
  importShow.value = false;
  getList()
  // 重新计算汇总数据
  calculateTotals();
}

defineExpose({
  getTableData: () => {
    // 创建一个深拷贝，避免直接修改原始数据
    const result = JSON.parse(JSON.stringify(tableData.value));

    // 遍历所有编辑中的数据，更新到结果中
    Object.keys(editableData).forEach(sid => {
      const index = result.findIndex(item => item.sid === sid);
      if (index !== -1) {
        // 只更新允许编辑的字段
        const editableFields = ['productName','supplier','englishBrand','origin','curr', 'unitPrice', 'unit','totalAmount','discountRate','discountAmount','planQuantity'];
        editableFields.forEach(field => {
          if (editableData[sid][field] !== undefined) {
            result[index][field] = editableData[sid][field];
          }
        });
      }
    });

    return result;
  },
  reloadData: getList, // 暴露刷新方法供父组件调用
  checkIsEditing: checkIsEditing, // 暴露检查编辑状态的方法
  save: save // 暴露保存方法
});
</script>

<style scoped>
.plan-detail-table {
  margin-top: 16px;
}

.cs-pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
}

.cs-margin-right {
  margin-right: 16px;
}

.cs-list-total-data {
  margin-right: 16px;
}

.count-number {
  margin-right: 8px;
}
</style>
