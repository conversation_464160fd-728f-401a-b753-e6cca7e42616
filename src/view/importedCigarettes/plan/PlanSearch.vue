<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >

    <!-- 数据状态 -->
    <a-form-item name="status"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="status">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.data_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 计划编号 -->
    <a-form-item name="planId"   :label="'计划编号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.planId" />
    </a-form-item>

    <!-- 制单日期 -->
    <a-form-item name="signDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="signDateFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="制单日期起"
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder="制单日期止"
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>

  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'PlanSearch'
})
const searchParam = reactive({
  planId: '',
  insertTimeFrom: '',
  insertTimeTo: '',
  status: ''
})

const locale = {
  lang: {
    locale: 'zh_CN',
    placeholder: 'Select date',
    rangePlaceholder: ['Start date', 'End date'],
    today: 'Today',
    now: 'Now',
    backToToday: 'Back to today',
    ok: 'Ok',
    clear: 'Clear',
    month: 'Month',
    year: 'Year',
    timeSelect: 'Select time',
    dateSelect: 'Select date',
    monthSelect: 'Choose a month',
    yearSelect: 'Choose a year',
    decadeSelect: 'Choose a decade',
    yearFormat: 'YYYY',
    dateFormat: 'M/D/YYYY',
    dayFormat: 'D',
    dateTimeFormat: 'M/D/YYYY HH:mm:ss',
    monthFormat: 'MMMM',
    monthBeforeYear: true,
    previousMonth: 'Previous month (PageUp)',
    nextMonth: 'Next month (PageDown)',
    previousYear: 'Last year (Control + left)',
    nextYear: 'Next year (Control + right)',
    previousDecade: 'Last decade',
    nextDecade: 'Next decade',
    previousCentury: 'Last century',
    nextCentury: 'Next century',
  },
}
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({
  searchParam,
  resetSearch
})
</script>

<style scoped>

</style>
