import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";
import params from "@/view/params";



// 进口合同信息列表
export const insertContract = (params) =>window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.insert, params)
export const updateContract = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.importedCigarettes.contract.update}/${sid}`, params)
export const deleteContract = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.importedCigarettes.contract.delete}/${sids}`)

export const updateContractList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.importedCigarettes.contractList.update}/${sid}`, params)
export const deleteContractList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.importedCigarettes.contractList.delete}/${sids}`)
export const confirmContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.confirm, params)
export const copyContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.copy, params)
export const sendAudit = (params) => window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.sendAudit, params)
export const invalidContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.cancel, params)
export const checkContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.checkStatus, params)
export const getContractTotal = (params) => window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contractList.getContractTotal, params)



/* 获取自定义配置信息 */
export const saveCustomWithDataId = (params)=>window.majesty.httpUtil.postAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/saveCustomWithDataId', params)
export const getCustomVaueByTypeAndDataId = (params) =>window.majesty.httpUtil.getAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/getCustomVaueByTypeAndDataId', params)
