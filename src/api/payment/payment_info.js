import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";



// 付款通知表头
export const insertNotifyHead = (params) =>window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.insert, params)
export const updateNotifyHead = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.payment.notifyHead.update}/${sid}`, params)
export const deleteNotifyHead = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.payment.notifyHead.delete}/${sids}`)
export const getDocNoNotifyHead = () => window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.getDocNo)
export const getRateHead = (curr) => window.majesty.httpUtil.postAction(`${ycCsApi.payment.notifyHead.getRate}/${curr}`)
export const getUserInfo = () => window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.getUserInfo)
export const confirmContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.confirm, params)
export const invalidContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.cancel, params)
export const backContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.back, params)
export const copyContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.copy, params)
export const printNotify = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.notifyHead.print, params)

// 付款通知表体
export const insertNotifyList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.payment.notifyList.insert, params)
export const insertOrderNotifyList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.payment.notifyList.insertOrder, params)
export const updateNotifyList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.payment.notifyList.update}/${sid}`, params)
export const deleteNotifyList = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.payment.notifyList.delete}`,params)

// 货款结算表头
export const insertSettlementList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.payment.settlement.insert, params)

export const updateSettlementList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.payment.settlement.update}/${sid}`, params)

export const confirmSettlement = (sid) => window.majesty.httpUtil.postAction(`${ycCsApi.payment.settlement.confirm}/${sid}`,)
export const deleteSettlementList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.payment.settlement.delete}/${sids}`)

export const backSettlement = (params) => window.majesty.httpUtil.postAction(ycCsApi.payment.settlement.back, params)

export const invalidatePlan = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.payment.settlement.invalidate}/${sids}`)


